{"settings_schema": {"colors": {"name": "Cores", "settings": {"background": {"label": "Plano de fundo"}, "background_gradient": {"label": "Gradiente de plano de fundo", "info": "O gradiente de plano de fundo substitui o plano de fundo sempre que possível."}, "text": {"label": "Texto"}, "button_background": {"label": "Plano de fundo do botão sólido"}, "button_label": {"label": "Etiqueta de botão sólido"}, "secondary_button_label": {"label": "Botão com contorno"}, "shadow": {"label": "Sombra"}}}, "typography": {"name": "Tipografia", "settings": {"type_header_font": {"label": "Fonte"}, "header__1": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header__2": {"content": "Corpo"}, "type_body_font": {"label": "Fonte"}, "heading_scale": {"label": "Escala"}, "body_scale": {"label": "Escala"}}}, "social-media": {"name": "Redes sociais", "settings": {"social_twitter_link": {"label": "X/Twitter", "info": "https://x.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "http://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "Contas de redes sociais"}}}, "currency_format": {"name": "Formato de moeda", "settings": {"currency_code_enabled": {"label": "Códigos de moeda"}, "paragraph": "Os preços do carrinho e do checkout sempre mostram os códigos de moeda"}}, "layout": {"name": "Layout", "settings": {"page_width": {"label": "<PERSON><PERSON><PERSON> da página"}, "spacing_sections": {"label": "Espaço entre as seções do modelo"}, "header__grid": {"content": "Grade"}, "paragraph__grid": {"content": "Afeta áreas com várias colunas ou linhas"}, "spacing_grid_horizontal": {"label": "Espaço horizontal"}, "spacing_grid_vertical": {"label": "Espaço vertical"}}}, "search_input": {"name": "Comportamento da pesquisa", "settings": {"predictive_search_enabled": {"label": "Sugestões de pesquisa"}, "predictive_search_show_vendor": {"label": "Fabricante do produto", "info": "Exibido quando as sugestões de pesquisa estão habilitadas"}, "predictive_search_show_price": {"label": "Preço do produto", "info": "Exibido quando as sugestões de pesquisa estão habilitadas"}}}, "global": {"settings": {"header__border": {"content": "<PERSON><PERSON>"}, "header__shadow": {"content": "Sombra"}, "blur": {"label": "Desfoque"}, "corner_radius": {"label": "Raio dos cantos"}, "horizontal_offset": {"label": "Compensação horizontal"}, "vertical_offset": {"label": "Compensação vertical"}, "thickness": {"label": "E<PERSON><PERSON><PERSON>"}, "opacity": {"label": "Opacidade"}, "image_padding": {"label": "Preenchimento de imagem"}, "text_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento do texto"}}}, "badges": {"name": "<PERSON><PERSON>", "settings": {"position": {"options__1": {"label": "Canto inferior esquerdo"}, "options__2": {"label": "Canto inferior direito"}, "options__3": {"label": "Canto superior esquerdo"}, "options__4": {"label": "Canto superior direito"}, "label": "Posição em cartões"}, "sale_badge_color_scheme": {"label": "Esquema de cores do selo de promoção"}, "sold_out_badge_color_scheme": {"label": "Esquema de cores do selo de esgotado"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "variant_pills": {"name": "Pílulas de variantes", "paragraph": "Pílulas de variante são uma forma de apresentar suas [variantes do produto](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#variant-picker-block)"}, "inputs": {"name": "Entradas"}, "content_containers": {"name": "Contêineres de conteúdo"}, "popups": {"name": "Menus suspensos e pop-ups", "paragraph": "Afeta áreas como menus suspensos de navegação, janelas modais de pop-up e pop-ups de carrinho"}, "media": {"name": "Mí<PERSON>"}, "drawers": {"name": "<PERSON><PERSON>"}, "cart": {"name": "<PERSON><PERSON><PERSON>", "settings": {"cart_type": {"label": "Tipo", "drawer": {"label": "Deslizante"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "notification": {"label": "Notificação pop-up"}}, "show_vendor": {"label": "Fabricante"}, "show_cart_note": {"label": "Observação do carrinho"}, "cart_drawer": {"header": "<PERSON><PERSON><PERSON> de compras deslizante", "collection": {"label": "Coleção", "info": "Exibido quando o carrinho de compras deslizante está vazio"}}}}, "cards": {"name": "Cartões de produtos", "settings": {"style": {"options__1": {"label": "Padrão"}, "options__2": {"label": "Cartão"}, "label": "<PERSON><PERSON><PERSON>"}}}, "collection_cards": {"name": "Cartões de coleção", "settings": {"style": {"options__1": {"label": "Padrão"}, "options__2": {"label": "Cartão"}, "label": "<PERSON><PERSON><PERSON>"}}}, "blog_cards": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"style": {"options__1": {"label": "Padrão"}, "options__2": {"label": "Cartão"}, "label": "<PERSON><PERSON><PERSON>"}}}, "logo": {"name": "Logo", "settings": {"logo_image": {"label": "Logo"}, "logo_width": {"label": "<PERSON><PERSON><PERSON>"}, "favicon": {"label": "Favicon", "info": "Exibido em 32 x 32px"}}}, "brand_information": {"name": "Informações da marca", "settings": {"brand_headline": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "brand_description": {"label": "Descrição"}, "brand_image": {"label": "Imagem"}, "brand_image_width": {"label": "<PERSON><PERSON><PERSON> da <PERSON>"}, "paragraph": {"content": "É exibido no bloco de informações da marca no rodapé"}}}, "animations": {"name": "Animações", "settings": {"animations_reveal_on_scroll": {"label": "Revelar seções durante a rolagem"}, "animations_hover_elements": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Elevação vertical"}, "label": "Efeito ao passar o mouse", "info": "Afeta cartões e botões", "options__3": {"label": "Elevação 3D"}}}}}, "sections": {"all": {"padding": {"section_padding_heading": "Preenchimento", "padding_top": "Acima", "padding_bottom": "Abaixo"}, "spacing": "Espaçamento", "colors": {"label": "Esquema de cores", "has_cards_info": "Atualize as configurações do tema para alterar o esquema de cores do cartão."}, "heading_size": {"label": "Tamanho do título", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}, "options__4": {"label": "Extra grande"}, "options__5": {"label": "Extraextragrande"}}, "image_shape": {"options__1": {"label": "Padrão"}, "options__2": {"label": "Arco"}, "options__3": {"label": "Bol<PERSON>"}, "options__4": {"label": "Chevron para esquerda"}, "options__5": {"label": "Chevron para direita"}, "options__6": {"label": "Diamante"}, "options__7": {"label": "Paralelogramo"}, "options__8": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "label": "Formato da imagem"}, "animation": {"content": "Animações", "image_behavior": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Movimentação do ambiente"}, "label": "Animação", "options__3": {"label": "Posição fixa do plano de fundo"}, "options__4": {"label": "Aumentar o zoom na rolagem"}}}}, "announcement-bar": {"name": "Barra de avisos", "blocks": {"announcement": {"name": "Comunicado", "settings": {"text": {"label": "Texto", "default": "Boas-vindas à nossa loja"}, "text_alignment": {"label": "Alinhamento do texto", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "link": {"label": "Link"}}}}, "settings": {"auto_rotate": {"label": "Girar automaticamente os comunicados"}, "change_slides_speed": {"label": "Mudar a cada"}, "show_social": {"label": "Ícones de redes sociais", "info": "[Gerenciar contas de rede social](/editor?context=theme&category=social%20media)"}, "enable_country_selector": {"label": "Se<PERSON>or de país/região", "info": "[Gerenciar países/regiões](/admin/settings/markets)"}, "enable_language_selector": {"label": "Se<PERSON>or de idioma", "info": "[Gerenciar idiomas](/admin/settings/languages)"}, "heading_utilities": {"content": "Serviços públicos"}, "paragraph": {"content": "Aparecer apenas em telas grandes"}}, "presets": {"name": "Barra de comunicados"}}, "collage": {"name": "Colagem", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Colagem multimídia"}, "desktop_layout": {"label": "Layout", "options__1": {"label": "Bloco grande primeiro"}, "options__2": {"label": "Bloco grande por último"}}, "mobile_layout": {"label": "Layout para dispositivo móvel", "options__1": {"label": "Colagem"}, "options__2": {"label": "Coluna"}}, "card_styles": {"label": "Estilo do cartão", "info": "Gerenciar estilos de cartão individual em [configurações do tema](/editor?context=theme&category=product%20cards)", "options__1": {"label": "Usar estilos de cartão individuais"}, "options__2": {"label": "Definir o estilo de todos como cartões de produto"}}, "header_layout": {"content": "Layout"}}, "blocks": {"image": {"name": "Imagem", "settings": {"image": {"label": "Imagem"}}}, "product": {"name": "Produ<PERSON>", "settings": {"product": {"label": "Produ<PERSON>"}, "secondary_background": {"label": "Exibir plano de fundo secundário"}, "second_image": {"label": "Exibir segunda imagem ao passar o cursor"}}}, "collection": {"name": "Coleção", "settings": {"collection": {"label": "Coleção"}}}, "video": {"name": "Vídeo", "settings": {"cover_image": {"label": "<PERSON><PERSON> de capa"}, "video_url": {"label": "URL", "info": "Os vídeos serão reproduzidos em uma janela pop-up se a seção tiver outros blocos.", "placeholder": "Usar um URL do YouTube ou do Vimeo"}, "description": {"label": "Texto alternativo do vídeo", "info": "Descreva o vídeo para clientes que usam leitores de tela. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)", "default": "Descreva o vídeo"}}}}, "presets": {"name": "Colagem"}}, "collection-list": {"name": "Lista de coleções", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Coleções"}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON><PERSON>"}, "show_view_all": {"label": "<PERSON><PERSON><PERSON> \"Ver tudo\"", "info": "Visível se a lista tiver mais coleções que as mostradas"}, "columns_desktop": {"label": "Colunas"}, "header_mobile": {"content": "Layout em dispositivos móveis"}, "columns_mobile": {"label": "Colunas", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "header_layout": {"content": "Layout"}}, "blocks": {"featured_collection": {"name": "Coleção", "settings": {"collection": {"label": "Coleção"}}}}, "presets": {"name": "Lista de coleções"}}, "contact-form": {"name": "Formulário de contato", "presets": {"name": "Formulário de contato"}, "settings": {"title": {"default": "Formulário de contato", "label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "custom-liquid": {"name": "Liquid personalizado", "settings": {"custom_liquid": {"label": "Código Liquid", "info": "Adicione snippets de app ou outros códigos do Liquid para criar personalizações avançadas. [Saiba mais](https://shopify.dev/docs/api/liquid)"}}, "presets": {"name": "Liquid personalizado"}}, "featured-blog": {"name": "Posts do blog", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Posts do blog"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Contagem de publicações"}, "show_view_all": {"label": "<PERSON><PERSON><PERSON> \"Ver tudo\"", "info": "Visível se o blog tiver mais publicações que as mostradas"}, "show_image": {"label": "Imagem em destaque"}, "show_date": {"label": "Data"}, "show_author": {"label": "Autoria"}, "columns_desktop": {"label": "Colunas"}, "layout_header": {"content": "Layout"}, "text_header": {"content": "Texto"}}, "presets": {"name": "Posts do blog"}}, "featured-collection": {"name": "Coleção em destaque", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Coleção em destaque"}, "collection": {"label": "Coleção"}, "products_to_show": {"label": "Contagem de produtos"}, "show_view_all": {"label": "<PERSON><PERSON><PERSON> \"Ver tudo\"", "info": "Visível se a coleção tiver mais produtos que os mostrados"}, "header": {"content": "Cartão de produto"}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}}, "show_secondary_image": {"label": "Exibir segunda imagem ao passar o cursor"}, "show_vendor": {"label": "Fabricante"}, "show_rating": {"label": "Classificação do produto", "info": "É necessário um app para classificações. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "enable_quick_buy": {"label": "<PERSON><PERSON><PERSON> rápida"}, "columns_desktop": {"label": "Colunas"}, "description": {"label": "Descrição"}, "show_description": {"label": "Mostrar a descrição da coleção do admin"}, "description_style": {"label": "Estilo da descrição", "options__1": {"label": "Corpo"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "Letras maiúsculas"}}, "view_all_style": {"label": "<PERSON><PERSON><PERSON> \"Ver tudo\"", "options__1": {"label": "Link"}, "options__2": {"label": "Botão com contorno"}, "options__3": {"label": "Botão sólido"}}, "enable_desktop_slider": {"label": "<PERSON><PERSON><PERSON>"}, "full_width": {"label": "Produtos de largura total"}, "header_mobile": {"content": "Layout em dispositivos móveis"}, "columns_mobile": {"label": "Colunas", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON><PERSON>"}, "header_text": {"content": "Texto"}, "header_collection": {"content": "Layout de coleções"}}, "presets": {"name": "Coleção em destaque"}}, "footer": {"name": "Rodapé", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON>"}, "menu": {"label": "<PERSON><PERSON>"}}}, "text": {"name": "Texto", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "subtext": {"label": "Subtexto", "default": "<p>Compartilhe informações de contato, detalhes da loja e conteúdo de marca com clientes.</p>"}}}, "brand_information": {"name": "Informações da marca", "settings": {"paragraph": {"content": "Gerenciar informações da marca em [configurações do tema](/editor?context=theme&category=brand%20information)"}, "show_social": {"label": "Ícones de redes sociais", "info": "[Gerenciar contas de rede social](/editor?context=theme&category=social%20media)"}}}}, "settings": {"newsletter_enable": {"label": "Assinante de e-mail"}, "newsletter_heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Assine nossos e-mails"}, "header__1": {"content": "Assinante de e-mail", "info": "As inscrições adicionam [perfis de cliente](https://help.shopify.com/manual/customers/manage-customers)"}, "show_social": {"label": "Ícones de redes sociais", "info": "[Gerenciar contas de rede social](/editor?context=theme&category=social%20media)"}, "enable_country_selector": {"label": "Se<PERSON>or de país/região", "info": "[Gerenciar países/regiões](/admin/settings/markets)"}, "enable_language_selector": {"label": "Se<PERSON>or de idioma", "info": "[Gerenciar idiomas](/admin/settings/languages)"}, "payment_enable": {"label": "Ícones de forma de pagamento"}, "margin_top": {"label": "Margem superior"}, "show_policy": {"label": "Links para políticas", "info": "[Gerenciar políticas](/admin/settings/legal)"}, "header__9": {"content": "Serviços públicos"}, "enable_follow_on_shop": {"label": "Se<PERSON>ir no <PERSON>", "info": "O Shop Pay deve estar habilitado. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}}}, "header": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"logo_position": {"label": "Posição do logo", "options__1": {"label": "Centralizado à esquerda"}, "options__2": {"label": "Canto superior esquerdo"}, "options__3": {"label": "Centralizado na parte superior"}, "options__4": {"label": "Centralizado"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "<PERSON><PERSON> separador<PERSON>"}, "margin_bottom": {"label": "Margem inferior"}, "menu_type_desktop": {"label": "Tipo de menu", "options__1": {"label": "<PERSON>u suspenso"}, "options__2": {"label": "Megamenu"}, "options__3": {"label": "Deslizante"}}, "mobile_logo_position": {"label": "Posição do logo em dispositivo móvel", "options__1": {"label": "Centro"}, "options__2": {"label": "E<PERSON>rda"}}, "logo_help": {"content": "Editar seu logo nas [configurações do tema](/editor?context=theme&category=logo)"}, "sticky_header_type": {"label": "Cabeçalho fixo", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Na rolagem da página para cima"}, "options__3": {"label": "Sempre"}, "options__4": {"label": "Se<PERSON>re, reduzir o tamanho do logo"}}, "enable_country_selector": {"label": "Se<PERSON>or de país/região", "info": "[Gerenciar países/regiões](/admin/settings/markets)"}, "enable_language_selector": {"label": "Se<PERSON>or de idioma", "info": "[Gerenciar idiomas](/admin/settings/languages)"}, "header__1": {"content": "Cor"}, "menu_color_scheme": {"label": "Esquema de cores do menu"}, "enable_customer_avatar": {"label": "Avatar de conta de cliente", "info": "Visível apenas quando os clientes fizerem login com o Shop. [Gerenciar contas de cliente](/admin/settings/customer_accounts)"}, "header__utilities": {"content": "Serviços públicos"}}}, "image-banner": {"name": "Banner de imagem", "settings": {"image": {"label": "Imagem 1"}, "image_2": {"label": "Imagem 2"}, "stack_images_on_mobile": {"label": "Empilhar imagens"}, "show_text_box": {"label": "Recipiente"}, "image_overlay_opacity": {"label": "Opacidade de sobreposição"}, "show_text_below": {"label": "Recipiente"}, "image_height": {"label": "Altura", "options__1": {"label": "Adaptar à primeira imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "options__4": {"label": "Grande"}}, "desktop_content_position": {"options__1": {"label": "Canto superior esquerdo"}, "options__2": {"label": "Centralizado na parte superior"}, "options__3": {"label": "Canto superior direito"}, "options__4": {"label": "Centralizado à esquerda"}, "options__5": {"label": "Centralizado"}, "options__6": {"label": "Centralizado à direita"}, "options__7": {"label": "Canto inferior esquerdo"}, "options__8": {"label": "Centralizado na parte inferior"}, "options__9": {"label": "Canto inferior direito"}, "label": "Posição"}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento"}, "mobile": {"content": "Layout em dispositivos móveis"}, "content": {"content": "<PERSON><PERSON><PERSON><PERSON>"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Banner grá<PERSON>o"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Texto", "default": "Dê informações a clientes sobre as imagens ou o conteúdo do banner no modelo."}, "text_style": {"options__1": {"label": "Corpo"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "Letras maiúsculas"}, "label": "<PERSON><PERSON><PERSON>"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label_1": {"label": "Etiqueta", "info": "Deixe em branco para ocultar", "default": "Etiqueta de botão"}, "button_link_1": {"label": "Link"}, "button_style_secondary_1": {"label": "Estilo com contorno"}, "button_label_2": {"label": "Etiqueta", "info": "Deixe em branco para ocultar", "default": "Etiqueta de botão"}, "button_link_2": {"label": "Link"}, "button_style_secondary_2": {"label": "Estilo com contorno"}, "header_1": {"content": "Botão 1"}, "header_2": {"content": "Botão 2"}}}}, "presets": {"name": "Banner de imagem"}}, "image-with-text": {"name": "Imagem com texto", "settings": {"image": {"label": "Imagem"}, "height": {"options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "label": "Altura", "options__4": {"label": "Grande"}}, "layout": {"options__1": {"label": "<PERSON>m primeiro"}, "options__2": {"label": "Segunda imagem"}, "label": "Posicionamento"}, "desktop_image_width": {"options__1": {"label": "Pequena"}, "options__2": {"label": "Média"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON><PERSON>"}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento"}, "desktop_content_position": {"options__1": {"label": "Parte superior"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Parte inferior"}, "label": "Posição"}, "content_layout": {"options__1": {"label": "Sem sobreposição"}, "options__2": {"label": "Com sobreposição"}, "label": "Layout"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento em dispositivos móveis"}, "header": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header_colors": {"content": "Cores"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Imagem com texto"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Texto", "default": "<p>Combine um texto com uma imagem para destacar o produto, a coleção ou o post do blog escolhido. Adicione informações sobre disponibilidade, estilo ou até mesmo uma avaliação.</p>"}, "text_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Corpo"}, "options__2": {"label": "Subtítulo"}}}}, "button": {"name": "Botão", "settings": {"button_label": {"label": "Etiqueta", "info": "Deixe em branco para ocultar", "default": "Etiqueta de botão"}, "button_link": {"label": "Link"}, "outline_button": {"label": "Estilo com contorno"}}}, "caption": {"name": "<PERSON>a", "settings": {"text": {"label": "Texto", "default": "Adicione um slogan"}, "text_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Subtítulo"}, "options__2": {"label": "Letras maiúsculas"}}, "caption_size": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}}}}}, "presets": {"name": "Imagem com texto"}}, "main-article": {"name": "Post do blog", "blocks": {"featured_image": {"name": "Imagem em destaque", "settings": {"image_height": {"label": "<PERSON><PERSON> da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequena"}, "options__3": {"label": "Média"}, "options__4": {"label": "Grande"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"blog_show_date": {"label": "Data"}, "blog_show_author": {"label": "Autoria"}}}, "content": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "share": {"name": "Compartilhar", "settings": {"text": {"label": "Texto", "default": "Compartilhar"}}}}}, "main-blog": {"name": "Posts do blog", "settings": {"show_image": {"label": "Imagem em destaque"}, "show_date": {"label": "Data"}, "show_author": {"label": "Autoria"}, "layout": {"label": "Layout", "options__1": {"label": "Grade"}, "options__2": {"label": "Colagem"}}, "image_height": {"label": "<PERSON><PERSON> da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequena"}, "options__3": {"label": "Média"}, "options__4": {"label": "Grande"}}}}, "main-cart-footer": {"name": "Subtotal", "blocks": {"subtotal": {"name": "Preço subtotal"}, "buttons": {"name": "Botão de checkout"}}}, "main-cart-items": {"name": "<PERSON><PERSON>"}, "main-collection-banner": {"name": "Banner da coleção", "settings": {"paragraph": {"content": "As informações da coleção são [gerenciadas no admin](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Descrição"}, "show_collection_image": {"label": "Imagem"}}}, "main-collection-product-grid": {"name": "Grade de produtos", "settings": {"products_per_page": {"label": "Produtos por página"}, "enable_filtering": {"label": "<PERSON><PERSON><PERSON>", "info": "Personalize os filtros [com o app Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_sorting": {"label": "Organização"}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}}, "show_secondary_image": {"label": "Exibir segunda imagem ao passar o cursor"}, "show_vendor": {"label": "Fabricante"}, "header__1": {"content": "Filtragem e organização"}, "header__3": {"content": "Cartão de produto"}, "enable_tags": {"label": "<PERSON><PERSON><PERSON>", "info": "Personalize os filtros [com o app Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "show_rating": {"label": "Classificação do produto", "info": "É necessário um app para classificações de produtos. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/collection-pages#product-grid-show-product-rating)"}, "columns_desktop": {"label": "Colunas"}, "columns_mobile": {"label": "Colunas em dispositivos móveis", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "filter_type": {"label": "Layout de filtro", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "<PERSON><PERSON>"}}, "quick_add": {"label": "<PERSON><PERSON><PERSON> rápida", "options": {"option_1": "<PERSON><PERSON><PERSON>", "option_2": "Padrão", "option_3": "Em massa"}}}}, "main-list-collections": {"name": "Página da lista de coleções", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Coleções"}, "sort": {"label": "Ordenar coleções", "options__1": {"label": "Ordem alfabética, A–Z"}, "options__2": {"label": "Ordem alfabética, Z–A"}, "options__3": {"label": "<PERSON>, mais recente primeiro"}, "options__4": {"label": "<PERSON>, mais antiga primeiro"}, "options__5": {"label": "Contagem de produtos, alta para baixa"}, "options__6": {"label": "Contagem de produtos, baixa para alta"}}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}}, "columns_desktop": {"label": "Colunas"}, "header_mobile": {"content": "Layout em dispositivos móveis"}, "columns_mobile": {"label": "Colunas em dispositivos móveis", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "main-page": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "main-password-footer": {"name": "<PERSON><PERSON><PERSON>"}, "main-password-header": {"name": "Cabeçal<PERSON>", "settings": {"logo_help": {"content": "Editar seu logo nas [configurações do tema](/editor?context=theme&category=logo)"}}}, "main-product": {"blocks": {"text": {"name": "Texto", "settings": {"text": {"label": "Texto", "default": "Bloco de texto"}, "text_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Corpo"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "Letras maiúsculas"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"name": "Preço"}, "quantity_selector": {"name": "Se<PERSON>or de quantidade"}, "variant_picker": {"name": "Se<PERSON>or de variante", "settings": {"picker_type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON>u suspenso"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "swatch_shape": {"label": "Amostra", "info": "<PERSON><PERSON> mais sobre as [amostras](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) nas opções de produtos", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Quadrado"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "buy_buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"show_dynamic_checkout": {"label": "Botões de checkout dinâmico", "info": "Os clientes verão a opção de pagamento preferida deles. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": " Opções de envio de cartão-presente", "info": "Os clientes podem adicionar uma mensagem pessoal e agendar a data de envio. [<PERSON><PERSON> mais](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}}, "pickup_availability": {"name": "Disponibilidade de retirada"}, "description": {"name": "Descrição"}, "share": {"name": "Compartilhar", "settings": {"text": {"label": "Texto", "default": "Compartilhar"}}}, "collapsible_tab": {"name": "<PERSON><PERSON> re<PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON> re<PERSON>"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON> da linha de página"}, "icon": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Maçã"}, "options__3": {"label": "Banana"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "options__5": {"label": "Caixa"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "Balão de chat"}, "options__8": {"label": "Marca de se<PERSON>ção"}, "options__9": {"label": "Pranchet<PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__11": {"label": "Sem lactose"}, "options__12": {"label": "Secador"}, "options__13": {"label": "<PERSON><PERSON><PERSON>"}, "options__14": {"label": "Fogo"}, "options__15": {"label": "<PERSON><PERSON>"}, "options__16": {"label": "Coração"}, "options__17": {"label": "<PERSON>rro"}, "options__18": {"label": "Fol<PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Relâmpago"}, "options__21": {"label": "<PERSON><PERSON>"}, "options__22": {"label": "Cadeado"}, "options__23": {"label": "Marcador de mapa"}, "options__24": {"label": "Sem nozes"}, "label": "Ícone", "options__25": {"label": "Calças"}, "options__26": {"label": "Pegada de pata"}, "options__27": {"label": "Pimenta"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Avião"}, "options__30": {"label": "Planta"}, "options__31": {"label": "Etiqueta de preço"}, "options__32": {"label": "Ponto de interrogação"}, "options__33": {"label": "Reciclar"}, "options__34": {"label": "Voltar"}, "options__35": {"label": "Régua"}, "options__36": {"label": "Travessa"}, "options__37": {"label": "<PERSON><PERSON>"}, "options__38": {"label": "Sapato"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Floco de neve"}, "options__41": {"label": "Estrela"}, "options__42": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__43": {"label": "Caminhão"}, "options__44": {"label": "<PERSON><PERSON><PERSON>"}}}}, "popup": {"name": "Pop-up", "settings": {"link_label": {"label": "Etiqueta de link", "default": "Texto do link pop-up"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "rating": {"name": "Avaliação do produto", "settings": {"paragraph": {"content": "É necessário um app para classificações de produtos. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#product-rating-block)"}}}, "complementary_products": {"name": "<PERSON><PERSON><PERSON>", "settings": {"paragraph": {"content": "Gerenciar produtos complementares no [app Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Combina bem com"}, "make_collapsible_row": {"label": "<PERSON><PERSON> re<PERSON>"}, "icon": {"info": "Exibido quando a linha recolhível é selecionada"}, "product_list_limit": {"label": "Contagem de produtos"}, "products_per_page": {"label": "Produtos por página"}, "pagination_style": {"label": "Paginação", "options": {"option_1": "Pontos", "option_2": "<PERSON><PERSON><PERSON>", "option_3": "Números"}}, "product_card": {"heading": "Cartão de produto"}, "image_ratio": {"label": "Proporção da imagem", "options": {"option_1": "Retrato", "option_2": "Quadrado"}}, "enable_quick_add": {"label": "<PERSON><PERSON><PERSON> rápida"}}}, "icon_with_text": {"name": "Ícone com texto", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}}, "heading": {"info": "Deixe em branco para ocultar este emparelhamento"}, "icon_1": {"label": "Ícone"}, "image_1": {"label": "Imagem"}, "heading_1": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "icon_2": {"label": "Ícone"}, "image_2": {"label": "Imagem"}, "heading_2": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "icon_3": {"label": "Ícone"}, "image_3": {"label": "Imagem"}, "heading_3": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "pairing_1": {"label": "Emparelhamento 1", "info": "Escolha um ícone ou adicione uma imagem para cada emparelhamento"}, "pairing_2": {"label": "Emparelhamento 2"}, "pairing_3": {"label": "Emparelhamento 3"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "Letras maiúsculas"}}}}, "inventory": {"name": "Status do estoque", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "Letras maiúsculas"}}, "inventory_threshold": {"label": "Limite de estoque baixo"}, "show_inventory_quantity": {"label": "Contagem de estoque"}}}}, "settings": {"header": {"content": "Mí<PERSON>"}, "enable_video_looping": {"label": "Repetir vídeo"}, "enable_sticky_info": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "hide_variants": {"label": "Ocultar a mídia de outras variantes depois que uma for selecionada"}, "gallery_layout": {"label": "Layout", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON> colu<PERSON>"}, "options__3": {"label": "Miniaturas"}, "options__4": {"label": "Carrossel de miniaturas"}}, "media_size": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}}, "mobile_thumbnails": {"label": "Layout em dispositivos móveis", "options__1": {"label": "<PERSON><PERSON> colu<PERSON>"}, "options__2": {"label": "<PERSON><PERSON>r miniaturas"}, "options__3": {"label": "Ocultar miniaturas"}}, "media_position": {"label": "Posição", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "image_zoom": {"label": "Zoom", "options__1": {"label": "<PERSON><PERSON><PERSON> janela modal"}, "options__2": {"label": "Clicar e passar o cursor"}, "options__3": {"label": "Sem zoom"}}, "constrain_to_viewport": {"label": "Ajustar à altura da tela"}, "media_fit": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Original"}, "options__2": {"label": "Preenchimento"}}}, "name": "Informações do produto"}, "main-search": {"name": "Resultados da pesquisa", "settings": {"image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}}, "show_secondary_image": {"label": "Exibir segunda imagem ao passar o cursor"}, "show_vendor": {"label": "Fabricante"}, "header__1": {"content": "Cartão de produto"}, "header__2": {"content": "Cartão de blog"}, "article_show_date": {"label": "Data"}, "article_show_author": {"label": "Autoria"}, "show_rating": {"label": "Classificação do produto", "info": "É necessário um app para classificações de produtos. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types/search-page)"}, "columns_desktop": {"label": "Colunas"}, "columns_mobile": {"label": "Colunas em dispositivos móveis", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "multicolumn": {"name": "Multicoluna", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "image_width": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Largura de um terço da coluna"}, "options__2": {"label": "Largura de metade da coluna"}, "options__3": {"label": "Largura total da coluna"}}, "image_ratio": {"label": "Proporção", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "column_alignment": {"label": "Alinham<PERSON><PERSON> da coluna", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}}, "background_style": {"label": "Fundo secundário", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Exibir como plano de fundo da coluna"}}, "button_label": {"label": "Etiqueta", "default": "Etiqueta de botão", "info": "Deixe em branco para ocultar"}, "button_link": {"label": "Link"}, "swipe_on_mobile": {"label": "<PERSON><PERSON><PERSON>"}, "columns_desktop": {"label": "Colunas"}, "header_mobile": {"content": "Layout em dispositivos móveis"}, "columns_mobile": {"label": "Colunas", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "header_text": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header_image": {"content": "Imagem"}, "header_layout": {"content": "Layout"}, "header_button": {"content": "Botão"}}, "blocks": {"column": {"name": "Coluna", "settings": {"image": {"label": "Imagem"}, "title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Coluna"}, "text": {"label": "Descrição", "default": "<p>Combine um texto com uma imagem para destacar o produto, a coleção ou o post do blog escolhido. Adicione informações sobre disponibilidade, estilo ou até mesmo uma avaliação.</p>"}, "link_label": {"label": "Etiqueta de link", "info": "Deixe em branco para ocultar"}, "link": {"label": "Link"}}}}, "presets": {"name": "Multicoluna"}}, "newsletter": {"name": "Assinante de e-mail", "settings": {"full_width": {"label": "Largura total"}, "paragraph": {"content": "As inscrições adicionam [perfis de cliente](https://help.shopify.com/manual/customers/manage-customers)"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Assine nossos e-mails"}}}, "paragraph": {"name": "Texto", "settings": {"paragraph": {"label": "Texto", "default": "<p>Seja a primeira pessoa a saber sobre novas coleções e ofertas exclusivas.</p>"}}}, "email_form": {"name": "Formulário de e-mail"}}, "presets": {"name": "Assinante de e-mail"}}, "page": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "rich-text": {"name": "Rich text", "settings": {"full_width": {"label": "Largura total"}, "desktop_content_position": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Posição do conteúdo"}, "content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento de conteúdo"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Fale sobre a marca"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Texto", "default": "<p>Compartilhe informações sobre a marca com clientes. Descreva um produto, faça comunicados ou dê as boas-vindas aos clientes na loja.</p>"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label_1": {"label": "Etiqueta", "info": "Deixe em branco para ocultar", "default": "Etiqueta de botão"}, "button_link_1": {"label": "Link"}, "button_style_secondary_1": {"label": "Estilo com contorno"}, "button_label_2": {"label": "Etiqueta", "info": "Deixe a etiqueta em branco para ocultar"}, "button_link_2": {"label": "Link"}, "button_style_secondary_2": {"label": "Estilo com contorno"}, "header_button1": {"content": "Botão 1"}, "header_button2": {"content": "Botão 2"}}}, "caption": {"name": "<PERSON>a", "settings": {"text": {"label": "Texto", "default": "Adicione um slogan"}, "text_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Subtítulo"}, "options__2": {"label": "Letras maiúsculas"}}, "caption_size": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}}}}}, "presets": {"name": "Rich text"}}, "apps": {"name": "Apps", "settings": {"include_margins": {"label": "<PERSON><PERSON>ar margens da seção iguais ao tema"}}, "presets": {"name": "Apps"}}, "video": {"name": "Vídeo", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Vídeo"}, "cover_image": {"label": "<PERSON><PERSON> de capa"}, "video_url": {"label": "URL", "info": "Usar um URL do YouTube ou do Vimeo"}, "description": {"label": "Texto alternativo do vídeo", "info": "Descreva o vídeo para quem usa leitores de tela"}, "image_padding": {"label": "Adicionar preenchimento de imagem", "info": "Selecione o preenchimento se não quiser que as imagens sejam cortadas."}, "full_width": {"label": "Largura total"}, "video": {"label": "Vídeo"}, "enable_video_looping": {"label": "Repetir vídeo"}, "header__1": {"content": "Vídeo hospedado na Shopify"}, "header__2": {"content": "Ou incorporar vídeo de URL"}, "header__3": {"content": "Layout"}, "paragraph": {"content": "Mostra quando nenhum vídeo hospedado na Shopify é selecionado"}}, "presets": {"name": "Vídeo"}}, "featured-product": {"name": "Produto em destaque", "blocks": {"text": {"name": "Texto", "settings": {"text": {"label": "Texto", "default": "Bloco de texto"}, "text_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Corpo"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "Letras maiúsculas"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"name": "Preço"}, "quantity_selector": {"name": "Se<PERSON>or de quantidade"}, "variant_picker": {"name": "Se<PERSON>or de variante", "settings": {"picker_type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON>u suspenso"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "swatch_shape": {"label": "Amostra", "info": "<PERSON><PERSON> mais sobre as [amostras](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) nas opções de produtos", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Quadrado"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "buy_buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"show_dynamic_checkout": {"label": "Botões de checkout dinâmico", "info": "Os clientes verão a opção de pagamento preferida deles. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Descrição"}, "share": {"name": "Compartilhar", "settings": {"featured_image_info": {"content": "Se você incluir um link em publicações nas redes sociais, a imagem em destaque da página será exibida como na pré-visualização. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "O título e a descrição da loja estão incluídos na prévia da imagem. [<PERSON><PERSON> mais](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Texto", "default": "Compartilhar"}}}, "rating": {"name": "Avaliação do produto", "settings": {"paragraph": {"content": "É necessário um app para classificações de produtos. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "Corpo"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "Letras maiúsculas"}}}}}, "settings": {"product": {"label": "Produ<PERSON>"}, "secondary_background": {"label": "Fundo secundário"}, "header": {"content": "Mí<PERSON>"}, "enable_video_looping": {"label": "Repetir vídeo"}, "hide_variants": {"label": "Ocultar mídia das variantes não selecionadas na área de trabalho"}, "media_position": {"label": "Posição", "info": "A posição é otimizada automaticamente para dispositivos móveis.", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}, "presets": {"name": "Produto em destaque"}}, "email-signup-banner": {"name": "Banner de assinante de e-mail", "settings": {"paragraph": {"content": "As inscrições adicionam [perfis de cliente](https://help.shopify.com/manual/customers/manage-customers)"}, "image": {"label": "Imagem de fundo"}, "show_background_image": {"label": "<PERSON><PERSON><PERSON> de fundo"}, "show_text_box": {"label": "Recipiente"}, "image_overlay_opacity": {"label": "Opacidade de sobreposição"}, "show_text_below": {"label": "Empilhar texto abaixo de imagens"}, "image_height": {"label": "Altura", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequena"}, "options__3": {"label": "Média"}, "options__4": {"label": "Grande"}}, "desktop_content_position": {"options__1": {"label": "Canto superior esquerdo"}, "options__2": {"label": "Centro da parte superior"}, "options__3": {"label": "Canto superior direito"}, "options__4": {"label": "Centro à esquerda"}, "options__5": {"label": "Centralizado"}, "options__6": {"label": "Centralizado à direita"}, "options__7": {"label": "Canto inferior esquerdo"}, "options__8": {"label": "Centralizado na parte inferior"}, "options__9": {"label": "Canto inferior direito"}, "label": "Posição"}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento"}, "header": {"content": "Layout em dispositivos móveis"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento"}, "color_scheme": {"info": "Vis<PERSON>vel quando o contêiner é exibido."}, "content_header": {"content": "<PERSON><PERSON><PERSON><PERSON>"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Abertura em breve"}}}, "paragraph": {"name": "Texto", "settings": {"paragraph": {"label": "Texto", "default": "<p><PERSON><PERSON> a primeira pessoa a saber quando lançarmos.</p>"}, "text_style": {"options__1": {"label": "Corpo"}, "options__2": {"label": "Subtítulo"}, "label": "<PERSON><PERSON><PERSON>"}}}, "email_form": {"name": "Formulário de e-mail"}}, "presets": {"name": "Banner de assinante de e-mail"}}, "slideshow": {"name": "Apresentação de slides", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Largura total"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "slide_height": {"label": "Altura", "options__1": {"label": "Adaptar à primeira imagem"}, "options__2": {"label": "Pequena"}, "options__3": {"label": "Média"}, "options__4": {"label": "Grande"}}, "slider_visual": {"label": "Paginação", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Pontos"}, "options__3": {"label": "Números"}}, "auto_rotate": {"label": "Girar automaticamente os slides"}, "change_slides_speed": {"label": "<PERSON><PERSON> os slides a cada"}, "show_text_below": {"label": "Empilhar texto abaixo de imagens"}, "mobile": {"content": "Layout em dispositivos móveis"}, "accessibility": {"content": "Acessibilidade", "label": "Descrição da apresentação de slides", "info": "Descreva a apresentação de slides para quem usa leitores de tela", "default": "Apresentação de slides sobre nossa marca"}}, "blocks": {"slide": {"name": "Slide", "settings": {"image": {"label": "Imagem"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Slide de imagem"}, "subheading": {"label": "Subtítulo", "default": "Conte a história de sua marca com vídeos e imagens"}, "button_label": {"label": "Etiqueta", "info": "Deixe em branco para ocultar", "default": "Etiqueta de botão"}, "link": {"label": "Link"}, "secondary_style": {"label": "Estilo com contorno"}, "box_align": {"label": "Posição do conteúdo", "options__1": {"label": "Canto superior esquerdo"}, "options__2": {"label": "Centralizado na parte superior"}, "options__3": {"label": "Canto superior direito"}, "options__4": {"label": "Centralizado à esquerda"}, "options__5": {"label": "Centralizado"}, "options__6": {"label": "Centralizado à direita"}, "options__7": {"label": "Canto inferior esquerdo"}, "options__8": {"label": "Centralizado na parte inferior"}, "options__9": {"label": "Canto inferior direito"}}, "show_text_box": {"label": "Recipiente"}, "text_alignment": {"label": "Alinhamento de conteúdo", "option_1": {"label": "E<PERSON>rda"}, "option_2": {"label": "Centro"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "image_overlay_opacity": {"label": "Opacidade de sobreposição"}, "text_alignment_mobile": {"label": "Alinhamento de conteúdo em dispositivos móveis", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "header_button": {"content": "Botão"}, "header_layout": {"content": "Layout"}, "header_text": {"content": "Texto"}, "header_colors": {"content": "Cores"}}}}, "presets": {"name": "Apresentação de slides"}}, "collapsible_content": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"caption": {"label": "<PERSON>a"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "heading_alignment": {"label": "Alinhamento do título", "options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "layout": {"label": "Recipiente", "options__1": {"label": "<PERSON><PERSON><PERSON> con<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "Contêiner de seção"}}, "container_color_scheme": {"label": "Esquema de cores do contêiner"}, "open_first_collapsible_row": {"label": "Imagem"}, "header": {"content": "Imagem"}, "image": {"label": "Imagem"}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Grande"}}, "desktop_layout": {"label": "Posicionamento", "options__1": {"label": "Primeira imagem"}, "options__2": {"label": "Segunda imagem"}}, "layout_header": {"content": "Layout"}, "section_color_scheme": {"label": "Esquema de cores da seção"}}, "blocks": {"collapsible_row": {"name": "<PERSON><PERSON> re<PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON> re<PERSON>"}, "row_content": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON> da linha de página"}, "icon": {"label": "Ícone", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Maçã"}, "options__3": {"label": "Banana"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "options__5": {"label": "Caixa"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "Balão de chat"}, "options__8": {"label": "Marca de se<PERSON>ção"}, "options__9": {"label": "Pranchet<PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__11": {"label": "Sem lactose"}, "options__12": {"label": "Secador"}, "options__13": {"label": "<PERSON><PERSON><PERSON>"}, "options__14": {"label": "Fogo"}, "options__15": {"label": "<PERSON><PERSON>"}, "options__16": {"label": "Coração"}, "options__17": {"label": "<PERSON>rro"}, "options__18": {"label": "Fol<PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Relâmpago"}, "options__21": {"label": "<PERSON><PERSON>"}, "options__22": {"label": "Cadeado"}, "options__23": {"label": "Marcador de mapa"}, "options__24": {"label": "Sem nozes"}, "options__25": {"label": "Calças"}, "options__26": {"label": "Pegada de pata"}, "options__27": {"label": "Pimenta"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Avião"}, "options__30": {"label": "Planta"}, "options__31": {"label": "Etiqueta de preço"}, "options__32": {"label": "Ponto de interrogação"}, "options__33": {"label": "Reciclar"}, "options__34": {"label": "Voltar"}, "options__35": {"label": "Régua"}, "options__36": {"label": "Travessa"}, "options__37": {"label": "<PERSON><PERSON>"}, "options__38": {"label": "Sapato"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Floco de neve"}, "options__41": {"label": "Estrela"}, "options__42": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__43": {"label": "Caminhão"}, "options__44": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "main-account": {"name": "Conta"}, "main-activate-account": {"name": "Ativação de conta"}, "main-addresses": {"name": "Endereços"}, "main-login": {"name": "Fazer login", "shop_login_button": {"enable": "Ativar a opção Fazer login com o Shop"}}, "main-order": {"name": "Pedido"}, "main-register": {"name": "Registro"}, "main-reset-password": {"name": "Redefinição de senha"}, "related-products": {"name": "Produtos relacionados", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "products_to_show": {"label": "Contagem de produtos"}, "columns_desktop": {"label": "Colunas"}, "paragraph__1": {"content": "Os produtos relacionados podem ser gerenciados no [app Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)", "default": "<PERSON><PERSON>z você também goste de"}, "header__2": {"content": "Cartão de produto"}, "image_ratio": {"label": "Proporção da imagem", "options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Quadrada"}}, "show_secondary_image": {"label": "Exibir segunda imagem ao passar o cursor"}, "show_vendor": {"label": "Fabricante"}, "show_rating": {"label": "Classificação do produto", "info": "É necessário um app para classificações de produtos. [<PERSON><PERSON> ma<PERSON>](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-product-recommendations)"}, "columns_mobile": {"label": "Colunas em dispositivos móveis", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "multirow": {"name": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "settings": {"image": {"label": "Imagem"}, "image_height": {"options__1": {"label": "Ada<PERSON>r à imagem"}, "options__2": {"label": "Pequeno"}, "options__3": {"label": "Médio"}, "options__4": {"label": "Grande"}, "label": "Altura"}, "desktop_image_width": {"options__1": {"label": "Pequeno"}, "options__2": {"label": "Médio"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON><PERSON>"}, "text_style": {"options__1": {"label": "Corpo"}, "options__2": {"label": "Subtítulo"}, "label": "Estilo de texto"}, "button_style": {"options__1": {"label": "Botão sólido"}, "options__2": {"label": "Botão com contorno"}, "label": "Estilo de b<PERSON>ão"}, "desktop_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento"}, "desktop_content_position": {"options__1": {"label": "Parte superior"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Parte inferior"}, "label": "Posição"}, "image_layout": {"options__1": {"label": "Alternar da esquerda"}, "options__2": {"label": "Alternar da direita"}, "options__3": {"label": "Alinhado à esquerda"}, "options__4": {"label": "Alinhado à direita"}, "label": "Posicionamento"}, "container_color_scheme": {"label": "Esquema de cores do contêiner"}, "mobile_content_alignment": {"options__1": {"label": "E<PERSON>rda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alinhamento em dispositivos móveis"}, "header": {"content": "Imagem"}, "header_2": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header_3": {"content": "Cores"}}, "blocks": {"row": {"name": "<PERSON><PERSON>", "settings": {"image": {"label": "Imagem"}, "caption": {"label": "<PERSON>a", "default": "<PERSON>a"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON>"}, "text": {"label": "Texto", "default": "<p>Combine um texto com uma imagem para destacar o produto, a coleção ou o post do blog escolhido. Adicione informações sobre disponibilidade, estilo ou até mesmo uma avaliação.</p>"}, "button_label": {"label": "Etiqueta de botão", "default": "Etiqueta de botão", "info": "Deixe em branco para ocultar"}, "button_link": {"label": "Link de botão"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}}, "quick-order-list": {"name": "Lista de pedidos rápidos", "settings": {"show_image": {"label": "Imagens"}, "show_sku": {"label": "SKUs"}, "variants_per_page": {"label": "<PERSON><PERSON><PERSON> por página"}}, "presets": {"name": "Lista de pedidos rápidos"}}}}