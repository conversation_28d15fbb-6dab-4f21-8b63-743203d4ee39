if (!customElements.get('countdown-timer')) {
  customElements.define(
    'countdown-timer',
    class CountdownTimer extends HTMLElement {
      constructor() {
        super();
        this.interval = null;
        this.targetDate = null;
        this.isExpired = false;
      }
 
      connectedCallback() {
        this.targetDate = this.getAttribute('data-target-date');
        if (!this.targetDate) {
          console.error('Countdown timer: No target date provided');
          this.renderError('No countdown date configured');
          return;
        }

        // Validate the date
        const testDate = new Date(this.targetDate);
        if (isNaN(testDate.getTime())) {
          console.error('Countdown timer: Invalid date format:', this.targetDate);
          this.renderError('Invalid countdown date format');
          return;
        }

        console.log('Countdown timer initialized for:', this.targetDate);
        this.render();
        this.startCountdown();
      }

      disconnectedCallback() {
        if (this.interval) {
          clearInterval(this.interval);
        }
      }

      render() {
        this.innerHTML = `
          <div class="countdown-timer">
            <div class="countdown-timer__content">
              <h3 class="countdown-timer__title">Coming Soon</h3>
              <div class="countdown-timer__display">
                <div class="countdown-timer__unit">
                  <span class="countdown-timer__number" data-days>00</span>
                  <span class="countdown-timer__label">Days</span>
                </div>
                <div class="countdown-timer__unit">
                  <span class="countdown-timer__number" data-hours>00</span>
                  <span class="countdown-timer__label">Hours</span>
                </div>
                <div class="countdown-timer__unit">
                  <span class="countdown-timer__number" data-minutes>00</span>
                  <span class="countdown-timer__label">Minutes</span>
                </div>
                <div class="countdown-timer__unit">
                  <span class="countdown-timer__number" data-seconds>00</span>
                  <span class="countdown-timer__label">Seconds</span>
                </div>
              </div>
              <div class="countdown-timer__message">
                <p>This product will be available for purchase when the countdown reaches zero.</p>
              </div>
            </div>
          </div>
        `;
      }

      renderError(message) {
        this.innerHTML = `
          <div class="countdown-timer countdown-timer--error">
            <div class="countdown-timer__content">
              <h3 class="countdown-timer__title">Configuration Error</h3>
              <div class="countdown-timer__message">
                <p>${message}</p>
                <p>Please contact support if this issue persists.</p>
              </div>
            </div>
          </div>
        `;
      }

      startCountdown() {
        this.updateCountdown();
        this.interval = setInterval(() => {
          this.updateCountdown();
        }, 1000);
      }

      updateCountdown() {
        const now = new Date().getTime();
        const target = new Date(this.targetDate).getTime();
        const difference = target - now;
        console.log('difference', difference <= 0);
        console.log(target, now);
        
        if (difference <= 0) {
          this.handleExpiration();
          return;
        }

        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        this.querySelector('[data-days]').textContent = this.padZero(days);
        this.querySelector('[data-hours]').textContent = this.padZero(hours);
        this.querySelector('[data-minutes]').textContent = this.padZero(minutes);
        this.querySelector('[data-seconds]').textContent = this.padZero(seconds);
      }

      handleExpiration() {
        if (this.interval) {
          clearInterval(this.interval);
        }

        this.isExpired = true;
        this.showExpiredState();
      }

      showExpiredState() {
        // Show loading state
        this.innerHTML = `
          <div class="countdown-timer countdown-timer--loading">
            <div class="countdown-timer__content">
              <h3 class="countdown-timer__title">Now Available!</h3>
              <div class="countdown-timer__loading">
                <div class="loading-spinner"></div>
                <p>Loading product details...</p>
              </div>
            </div>
          </div>
        `;

        // Reload the page to show the product details
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }

      padZero(num) {
        return num.toString().padStart(2, '0');
      }

      static get observedAttributes() {
        return ['data-target-date'];
      }

      attributeChangedCallback(name, oldValue, newValue) {
        if (name === 'data-target-date' && oldValue !== newValue) {
          this.targetDate = newValue;
          if (this.interval) {
            clearInterval(this.interval);
          }
          this.startCountdown();
        }
      }
    }
  );
}
