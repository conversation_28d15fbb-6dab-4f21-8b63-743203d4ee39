document.addEventListener('DOMContentLoaded', () => {
	const swiperSlider = document.querySelector('[id^="ProductModal-"] swiper-slider');
	let swiper;
	let zoomedIn = false;
	let currentScale = 1;
	const zoomStep = 1;
	const maxZoom = 3;
	const minZoom = 1;
	let currentX = 0;
	let currentY = 0;
	let dragContainer = null;

	const btnZoomIn = document.getElementById('zoom-in');
	const btnZoomOut = document.getElementById('zoom-out');

	let isDragging = false;
	let startX = 0;
	let startY = 0;
	let velocityX = 0;
	let velocityY = 0;
	let lastMoveTime = 0;
	let dragRAF = null;
	let wasDragged = false;
	let initialPinchDistance = null;
	let initialScale = 1;
	

	function getActiveZoomContainer() {
		return swiper.slides[swiper.activeIndex]?.querySelector('.swiper-zoom-container');
	}

	function applyTransform() {
		if (!dragContainer) return;

		const bounds = getPanLimits(dragContainer);
		currentX = Math.max(bounds.minX, Math.min(bounds.maxX, currentX));
		currentY = Math.max(bounds.minY, Math.min(bounds.maxY, currentY));

		dragContainer.style.transform = `translate3d(${currentX}px, ${currentY}px, 0) scale(${currentScale})`;
	}

	function getPanLimits(container) {
		const img = container.querySelector('img');
		if (!img) return { minX: 0, maxX: 0, minY: 0, maxY: 0 };

		const containerRect = container.getBoundingClientRect();
		const imgRect = img.getBoundingClientRect();

		const scale = currentScale;

		const scaledWidth = img.naturalWidth * scale;
		const scaledHeight = img.naturalHeight * scale;

		// const overflowX = Math.max((scaledWidth - containerRect.width) / 2, 0);
		// const overflowY = Math.max((scaledHeight - containerRect.height) / 2, 0);
		const overflowX = Math.abs((scaledWidth - containerRect.width) / 2);
		const overflowY = Math.abs((scaledHeight - containerRect.height) / 2);

		return {
			minX: -overflowX,
			maxX: overflowX,
			minY: -overflowY,
			maxY: overflowY,
		};
	}

	function updateZoomButtons() {
		btnZoomIn.disabled = currentScale >= maxZoom;
		btnZoomOut.disabled = currentScale <= minZoom;
	}

	function resetZoom() {
		const container = getActiveZoomContainer();
		if (!container) return;

		currentScale = 1;
		currentX = 0;
		currentY = 0;
		zoomedIn = false;

		container.style.transition = 'transform 0.3s ease';
		container.style.transform = `translate3d(0px, 0px) scale(1)`;

		swiper.allowTouchMove = true;
		updateZoomButtons();
	}

	function zoomImage(step) {
		const container = getActiveZoomContainer();
		if (!container) return;

		currentScale = Math.max(minZoom, Math.min(maxZoom, currentScale + step));
		container.style.transition = 'transform 0.3s ease';
		container.style.transform = `translate3d(${currentX}px, ${currentY}px, 0) scale(${currentScale})`;

		zoomedIn = currentScale > 1;
		applyTransform();
		updateZoomButtons();
	}

	function enableDrag(container) {
		dragContainer = container;

		container.addEventListener('mousedown', startDrag);
		container.addEventListener('touchstart', startDrag, { passive: false });

		container.addEventListener('mousemove', drag);
		container.addEventListener('touchmove', drag, { passive: false });

		container.addEventListener('mouseup', endDrag);
		container.addEventListener('mouseleave', endDrag);
		container.addEventListener('touchend', endDrag);
		container.addEventListener('touchcancel', endDrag);
		container.addEventListener('touchstart', handlePinchStart, { passive: false });
		container.addEventListener('touchmove', handlePinchMove, { passive: false });
		container.addEventListener('touchend', handlePinchEnd);
		container.addEventListener('touchcancel', handlePinchEnd);
	}

	function startDrag(e) {
		wasDragged = false;
		if (currentScale <= 1) return;

		if (!dragContainer) {
			dragContainer = getActiveZoomContainer();
			if (!dragContainer) return;
		}
		
		isDragging = true;
		const evt = e.type.startsWith('touch') ? e.touches[0] : e;
		startX = evt.clientX;
		startY = evt.clientY;
		
		velocityX = 0;
		velocityY = 0;
		lastMoveTime = Date.now();

		dragContainer.closest('.swiper').classList.add('is-dragging');
		cancelAnimationFrame(dragRAF);
	}

	function drag(e) {
		if (!isDragging || currentScale <= 1) return;

		e.preventDefault();
		const evt = e.type.startsWith('touch') ? e.touches[0] : e;

		const now = Date.now();
		const deltaT = now - lastMoveTime;

		const deltaX = evt.clientX - startX;
		const deltaY = evt.clientY - startY;

		currentX += deltaX;
		currentY += deltaY;

		velocityX = deltaX / deltaT;
		velocityY = deltaY / deltaT;

		startX = evt.clientX;
		startY = evt.clientY;

		applyTransform();
		lastMoveTime = now;
		if (Math.abs(deltaX) > 2 || Math.abs(deltaY) > 2) {
			wasDragged = true;
		}
	}

	function endDrag() {
		if (!isDragging) return;
		isDragging = false;

		dragContainer.closest('.swiper').classList.remove('is-dragging');

		const friction = 0.90;
		function momentum() {
			if (Math.abs(velocityX) < 0.05 && Math.abs(velocityY) < 0.05) return;

			currentX += velocityX * 16;
			currentY += velocityY * 16;

			velocityX *= friction;
			velocityY *= friction;

			applyTransform();
			dragRAF = requestAnimationFrame(momentum);
		}

		momentum();
	}

	function getDistance(touches) {
		const dx = touches[0].clientX - touches[1].clientX;
		const dy = touches[0].clientY - touches[1].clientY;
		return Math.sqrt(dx * dx + dy * dy);
	}

	function handlePinchStart(e) {
		if (e.touches.length !== 2) return;

		e.preventDefault();
		initialPinchDistance = getDistance(e.touches);
		initialScale = currentScale;
	}

	function handlePinchMove(e) {
		if (e.touches.length !== 2 || initialPinchDistance === null) return;

		e.preventDefault();
		const currentDistance = getDistance(e.touches);
		const scaleChange = currentDistance / initialPinchDistance;

		currentScale = Math.max(minZoom, Math.min(maxZoom, initialScale * scaleChange));
		zoomedIn = currentScale > 1;

		applyTransform();
		updateZoomButtons();
	}

	function handlePinchEnd(e) {
		if (e.touches.length < 2) {
			initialPinchDistance = null;
		}
	}
	
	// Zoom buttons
	btnZoomIn.addEventListener('click', () => zoomImage(zoomStep));
	btnZoomOut.addEventListener('click', () => zoomImage(-zoomStep));

	const sliderConfig = {
		slidesPerView: 1,
		spaceBetween: 0,
		loop: true,
		freeMode: false,
		zoom: {
			maxRatio: 3,
			toggle: false
		},
		navigation: {
			nextEl: ".swiper-button-next",
			prevEl: ".swiper-button-prev"
		},
		pagination: {
			el: ".swiper-pagination",
			clickable: true
		},
		on: {
			init(swiperInstance) {
				swiper = swiperInstance;

				// Tap to toggle zoom
				swiper.slides.forEach((slide) => {
					const container = slide.querySelector('.swiper-zoom-container');
					if (!container) return;

					container.addEventListener('click', () => {
						if (wasDragged) {
							wasDragged = false;
							return;
						}

						if (zoomedIn) {
							currentScale = 1;
							currentX = 0;
							currentY = 0;
							zoomedIn = false;

							container.style.transition = 'transform 0.3s ease';
							container.style.transform = `translate3d(0px, 0px) scale(1)`;
							swiper.allowTouchMove = true;
						} else {
							currentScale = 2;
							currentX = 0;
							currentY = 0;
							zoomedIn = true;

							container.style.transition = 'transform 0.3s ease';
							container.style.transform = `translate3d(0px, 0px) scale(${currentScale})`;
							swiper.allowTouchMove = false;
						}

						dragContainer = container;
						applyTransform();
						updateZoomButtons();
					});

					enableDrag(container);
				});

				updateZoomButtons();
			},
			slideChangeTransitionStart() {
				resetZoom();

				const newContainer = getActiveZoomContainer();
				if (newContainer) {
					dragContainer = newContainer;
					enableDrag(newContainer);
				}
			}
		},
		breakpoints: {
			576: { slidesPerView: 1, spaceBetween: 0 },
			768: { slidesPerView: 1, spaceBetween: 0 },
			992: { slidesPerView: 1, spaceBetween: 0 },
			1200: { slidesPerView: 1, spaceBetween: 0 },
		},
	};

	if (swiperSlider) {
		swiperSlider.config = sliderConfig;
	}
});