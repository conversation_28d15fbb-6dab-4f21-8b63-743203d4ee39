{{ 'section-image-banner.css' | asset_url | stylesheet_tag }}
{{ 'component-newsletter.css' | asset_url | stylesheet_tag }}
{{ 'newsletter-section.css' | asset_url | stylesheet_tag }}
{{ 'section-email-signup-banner.css' | asset_url | stylesheet_tag }}

{%- style -%}
  #Banner-{{ section.id }}::after {
    opacity: {{ section.settings.image_overlay_opacity | divided_by: 100.0 }};
  }

  {%- if section.settings.image_height == 'adapt-image' and section.settings.image != blank -%}
    @media screen and (max-width: 749px) {
      #Banner-{{ section.id }}:not(.banner--mobile-bottom) .banner__content::before {
        padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%;
        content: '';
        display: block;
      }
    }

    #Banner-{{ section.id }}::before,
    #Banner-{{ section.id }} .banner__media::before {
      padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%;
      content: '';
      display: block;
    }
  {%- endif -%}
{%- endstyle -%}

<div
  id="Banner-{{ section.id }}"
  class="email-signup-banner banner banner--content-align-{{ section.settings.desktop_content_alignment }} banner--content-align-mobile-{{ section.settings.mobile_content_alignment }} banner--{{ section.settings.image_height }}{% if section.settings.image_height == 'adapt-image' and section.settings.image != blank %} banner--adapt{% endif %}{% if section.settings.show_text_below %} banner--mobile-bottom{%- endif -%}{% if section.settings.show_text_box == false %} banner--desktop-transparent{% endif %}"
>
  {%- if section.settings.show_background_image -%}
    <div class="banner__media{% if section.settings.image != blank %} media{% endif %}">
      {%- if section.settings.image != blank -%}
        <img
          srcset="
            {%- if section.settings.image.width >= 375 -%}{{ section.settings.image | image_url: width: 375 }} 375w,{%- endif -%}
            {%- if section.settings.image.width >= 750 -%}{{ section.settings.image | image_url: width: 750 }} 750w,{%- endif -%}
            {%- if section.settings.image.width >= 1100 -%}{{ section.settings.image | image_url: width: 1100 }} 1100w,{%- endif -%}
            {%- if section.settings.image.width >= 1500 -%}{{ section.settings.image | image_url: width: 1500 }} 1500w,{%- endif -%}
            {%- if section.settings.image.width >= 1780 -%}{{ section.settings.image | image_url: width: 1780 }} 1780w,{%- endif -%}
            {%- if section.settings.image.width >= 2000 -%}{{ section.settings.image | image_url: width: 2000 }} 2000w,{%- endif -%}
            {%- if section.settings.image.width >= 3000 -%}{{ section.settings.image | image_url: width: 3000 }} 3000w,{%- endif -%}
            {%- if section.settings.image.width >= 3840 -%}{{ section.settings.image | image_url: width: 3840 }} 3840w,{%- endif -%}
            {{ section.settings.image | image_url }} {{ section.settings.image.width }}w
          "
          sizes="100vw"
          src="{{ section.settings.image | image_url: width: 1500 }}"
          loading="lazy"
          alt="{{ section.settings.image.alt | escape }}"
          width="{{ section.settings.image.width }}"
          height="{{ section.settings.image.width | divided_by: section.settings.image.aspect_ratio }}"
        >
      {%- else -%}
        {{- 'email-signup-banner-background.svg' | inline_asset_content -}}
        {{- 'email-signup-banner-background-mobile.svg' | inline_asset_content -}}
      {%- endif -%}
    </div>
  {%- endif -%}
  <div class="banner__content banner__content--{{ section.settings.desktop_content_position }} page-width">
    <div class="email-signup-banner__box banner__box newsletter newsletter__wrapper isolate{% if section.settings.show_background_image == false %} email-signup-banner__box--no-image{% endif %} content-container color-{{ section.settings.color_scheme }} gradient content-container--full-width-mobile">
      {%- for block in section.blocks -%}
        {%- case block.type -%}
          {%- when 'heading' -%}
            <h2 class="email-signup-banner__heading {{ block.settings.heading_size }}" {{ block.shopify_attributes }}>
              {{ block.settings.heading | escape }}
            </h2>
          {%- when 'paragraph' -%}
            <div class="newsletter__subheading rte {{ block.settings.text_style }}" {{ block.shopify_attributes }}>
              {{ block.settings.text }}
            </div>
          {%- when 'email_form' -%}
            <div {{ block.shopify_attributes }}>
              {% form 'customer', class: 'newsletter-form' %}
                <input type="hidden" name="contact[tags]" value="newsletter">
                <div class="newsletter-form__field-wrapper">
                  <div class="field">
                    <input
                      id="NewsletterForm--{{ section.id }}"
                      type="email"
                      name="contact[email]"
                      class="field__input"
                      value="{{ form.email }}"
                      aria-required="true"
                      autocorrect="off"
                      autocapitalize="off"
                      autocomplete="email"
                      {% if form.errors %}
                        autofocus
                        aria-invalid="true"
                        aria-describedby="Newsletter-error--{{ section.id }}"
                      {% elsif form.posted_successfully? %}
                        aria-describedby="Newsletter-success--{{ section.id }}"
                      {% endif %}
                      placeholder="{{ 'newsletter.label' | t }}"
                      required
                    >
                    <label class="field__label" for="NewsletterForm--{{ section.id }}">
                      {{ 'newsletter.label' | t }}
                    </label>
                    <button
                      type="submit"
                      class="newsletter-form__button field__button"
                      name="commit"
                      id="Subscribe"
                      aria-label="{{ 'newsletter.button_label' | t }}"
                    >
                      <span class="svg-wrapper">
                        {{- 'icon-arrow.svg' | inline_asset_content -}}
                      </span>
                    </button>
                  </div>
                  {%- if form.errors -%}
                    <small class="newsletter-form__message form__message" id="Newsletter-error--{{ section.id }}">
                      <span class="svg-wrapper">
                        {{- 'icon-error.svg' | inline_asset_content -}}
                      </span>
                      {{- form.errors.translated_fields.email | capitalize }}
                      {{ form.errors.messages.email -}}
                    </small>
                  {%- endif -%}
                </div>
                {%- if form.posted_successfully? -%}
                  <h3
                    class="newsletter-form__message newsletter-form__message--success form__message"
                    id="Newsletter-success--{{ section.id }}"
                    tabindex="-1"
                    autofocus
                  >
                    <span class="svg-wrapper">
                      {{- 'icon-success.svg' | inline_asset_content -}}
                    </span>
                    {{- 'newsletter.success' | t }}
                  </h3>
                {%- endif -%}
              {% endform %}
            </div>
        {%- endcase -%}
      {%- endfor -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.email-signup-banner.name",
  "tag": "section",
  "class": "section",
  "enabled_on": {
    "templates": ["password"]
  },
  "settings": [
    {
      "type": "paragraph",
      "content": "t:sections.email-signup-banner.settings.paragraph.content"
    },
    {
      "type": "checkbox",
      "id": "show_background_image",
      "label": "t:sections.email-signup-banner.settings.show_background_image.label",
      "default": true
    },    
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.email-signup-banner.settings.image.label"
    },
    {
      "type": "range",
      "id": "image_overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 10,
      "unit": "%",
      "label": "t:sections.email-signup-banner.settings.image_overlay_opacity.label",
      "default": 0
    },
    {
      "type": "select",
      "id": "image_height",
      "options": [
        {
          "value": "adapt-image",
          "label": "t:sections.email-signup-banner.settings.image_height.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.email-signup-banner.settings.image_height.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.email-signup-banner.settings.image_height.options__3.label"
        },
        {
          "value": "large",
          "label": "t:sections.email-signup-banner.settings.image_height.options__4.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.email-signup-banner.settings.image_height.label"
    },
    {
      "type": "header",
      "content": "t:sections.email-signup-banner.settings.content_header.content"
    },    
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "top-left",
          "label": "t:sections.email-signup-banner.settings.desktop_content_position.options__1.label"
        },
        {
          "value": "top-center",
          "label": "t:sections.email-signup-banner.settings.desktop_content_position.options__2.label"
        },
        {
          "value": "top-right",
          "label": "t:sections.email-signup-banner.settings.desktop_content_position.options__3.label"
        },
        {
          "value": "middle-left",
          "label": "t:sections.email-signup-banner.settings.desktop_content_position.options__4.label"
        },
        {
          "value": "middle-center",
          "label": "t:sections.email-signup-banner.settings.desktop_content_position.options__5.label"
        },
        {
          "value": "middle-right",
          "label": "t:sections.email-signup-banner.settings.desktop_content_position.options__6.label"
        },
        {
          "value": "bottom-left",
          "label": "t:sections.email-signup-banner.settings.desktop_content_position.options__7.label"
        },
        {
          "value": "bottom-center",
          "label": "t:sections.email-signup-banner.settings.desktop_content_position.options__8.label"
        },
        {
          "value": "bottom-right",
          "label": "t:sections.email-signup-banner.settings.desktop_content_position.options__9.label"
        }
      ],
      "default": "middle-center",
      "label": "t:sections.email-signup-banner.settings.desktop_content_position.label"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.email-signup-banner.settings.desktop_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.email-signup-banner.settings.desktop_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.email-signup-banner.settings.desktop_content_alignment.options__3.label"
        }
      ],
      "default": "center",
      "label": "t:sections.email-signup-banner.settings.desktop_content_alignment.label"
    },    
    {
      "type": "checkbox",
      "id": "show_text_box",
      "default": true,
      "label": "t:sections.email-signup-banner.settings.show_text_box.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "info": "t:sections.email-signup-banner.settings.color_scheme.info",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.email-signup-banner.settings.header.content"
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.email-signup-banner.settings.mobile_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.email-signup-banner.settings.mobile_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.email-signup-banner.settings.mobile_content_alignment.options__3.label"
        }
      ],
      "default": "center",
      "label": "t:sections.email-signup-banner.settings.mobile_content_alignment.label"
    },
    {
      "type": "checkbox",
      "id": "show_text_below",
      "default": true,
      "label": "t:sections.email-signup-banner.settings.show_text_below.label"
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "t:sections.email-signup-banner.blocks.heading.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "t:sections.email-signup-banner.blocks.heading.settings.heading.default",
          "label": "t:sections.email-signup-banner.blocks.heading.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            },
            {
              "value": "hxl",
              "label": "t:sections.all.heading_size.options__4.label"
            },
            {
              "value": "hxxl",
              "label": "t:sections.all.heading_size.options__5.label"
            }
          ],
          "default": "h1",
          "label": "t:sections.all.heading_size.label"
        }
      ]
    },
    {
      "type": "paragraph",
      "name": "t:sections.email-signup-banner.blocks.paragraph.name",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "t:sections.email-signup-banner.blocks.paragraph.settings.paragraph.default",
          "label": "t:sections.email-signup-banner.blocks.paragraph.settings.paragraph.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.email-signup-banner.blocks.paragraph.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.email-signup-banner.blocks.paragraph.settings.text_style.options__2.label"
            }
          ],
          "default": "body",
          "label": "t:sections.email-signup-banner.blocks.paragraph.settings.text_style.label"
        }
      ]
    },
    {
      "type": "email_form",
      "name": "t:sections.email-signup-banner.blocks.email_form.name",
      "limit": 1
    }
  ],
  "presets": [
    {
      "name": "t:sections.email-signup-banner.presets.name",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "paragraph"
        },
        {
          "type": "email_form"
        }
      ]
    }
  ]
}
{% endschema %}
