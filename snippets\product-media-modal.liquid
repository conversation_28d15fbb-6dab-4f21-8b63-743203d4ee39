{% comment %}
  Renders a product media modal. Also see 'product-media-gallery'

  Accepts:
  - product: {Object} Product liquid object
  - variant_images: {Array} Product images associated with a variant

  Usage:
  {% render 'product-media-modal' %}
{% endcomment %}

<product-modal id="ProductModal-{{ section.id }}" class="product-media-modal">
  <div
    class="product-media-modal__dialog color-{{ section.settings.color_scheme }} gradient"
    role="dialog"
    aria-label="{{ 'products.modal.label' | t }}"
    aria-modal="true"
    tabindex="-1"
  >
  <div class="product-media-modal__toggle product-media-modal__dialog--btns">
    <button
      id="zoom-out"
      type="button"
      class="product-media-modal__zoom-btn"
      aria-label="{{ 'accessibility.zoom_out' | t }}"
      aria-disabled="false"
    >
      {{ 'icon-minus.svg' | inline_asset_content }}
    </button>
    <button
      id="zoom-in"
      type="button"
      class="product-media-modal__zoom-btn"
      aria-label="{{ 'accessibility.zoom_in' | t }}"
      aria-disabled="false"
    >
      {{ 'icon-plus.svg' | inline_asset_content }}
    </button>
    <button
      id="ModalClose-{{ section.id }}"
      type="button"
      class="product-media-modal__close-btn"
      aria-label="{{ 'accessibility.close' | t }}"
    >
      {{ 'icon-close.svg' | inline_asset_content }}
    </button>
  </div>

    <div
      class="product-media-modal__content color-{{ section.settings.color_scheme }} gradient"
      role="document"
      aria-label="{{ 'products.modal.label' | t }}"
      tabindex="0"
    >
      <swiper-slider>
        <div class="swiper product-media-zoom">
          <div class="swiper-wrapper">
            {% comment %} {%- if product.selected_or_first_available_variant.featured_media != null -%}
              {%- assign media = product.selected_or_first_available_variant.featured_media -%}

              <div class="swiper-slide">
                <div class="swiper-zoom-container">
                  {%- render 'product-media', media: media, loop: section.settings.enable_video_looping, variant_image: section.settings.hide_variants -%}
                </div>
              </div>
            {%- endif -%} {% endcomment %}

            {%- for media in product.media -%}
              <div class="swiper-slide ts-select-none" data-media-id="{{ media.id }}">
                <div class="swiper-zoom-container">
                  {%- liquid
                    if section.settings.hide_variants and variant_images contains media.src or variant_images contains media.id
                      assign variant_image = true
                    else
                      assign variant_image = false
                    endif

                    render 'product-media', media: media, loop: section.settings.enable_video_looping, variant_image: variant_image
                  -%}
                </div>
              </div>
            {%- endfor -%}
          </div>
        </div>
        <div class="swiper-pagination swiper-pagination-bullets bullets--{{ section.id }} !ts-top-[calc(100dvh-8rem)]"></div>

        <div class="swiper-controls">
          <div class="swiper-nav swiper-button-prev swiper-prev--{{ section.id }}">{{- 'icon-swiper-arrow.svg' | inline_asset_content -}}</div>
          <div class="swiper-nav swiper-button-next swiper-nxt--{{ section.id }}">{{- 'icon-swiper-arrow.svg' | inline_asset_content -}}</div>
        </div>
      </swiper-slider>
    </div>
  </div>
</product-modal>
