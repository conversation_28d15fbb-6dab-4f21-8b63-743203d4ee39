<product-info
  id="MainProduct-{{ section.id }}"
  class="section-{{ section.id }}-padding gradient color-{{ section.settings.color_scheme }}"
  data-section="{{ section.id }}"
  data-product-id="{{ product.id }}"
  data-update-url="true"
  data-url="{{ product.url }}"
  {% if section.settings.image_zoom == 'hover' %}
    data-zoom-on-hover
  {% endif %}
>
  {{ 'section-main-product.css' | asset_url | stylesheet_tag }}
  {{ 'component-accordion.css' | asset_url | stylesheet_tag }}
  {{ 'component-price.css' | asset_url | stylesheet_tag }}
  {{ 'component-slider.css' | asset_url | stylesheet_tag }}
  {{ 'component-rating.css' | asset_url | stylesheet_tag }}
  {{ 'component-deferred-media.css' | asset_url | stylesheet_tag }}

  {% unless product.has_only_default_variant %}
    {{ 'component-product-variant-picker.css' | asset_url | stylesheet_tag }}
    {{ 'component-swatch-input.css' | asset_url | stylesheet_tag }}
    {{ 'component-swatch.css' | asset_url | stylesheet_tag }}
  {% endunless %}
  {%- if product.quantity_price_breaks_configured? -%}
    {{ 'component-volume-pricing.css' | asset_url | stylesheet_tag }}
  {%- endif -%}

  {%- style -%}
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
      padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top }}px;
        padding-bottom: {{ section.settings.padding_bottom }}px;
      }
    }
  {%- endstyle -%}

  <script src="{{ 'product-info.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
  {%- if product.quantity_price_breaks_configured? -%}
    <script src="{{ 'show-more.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'price-per-item.js' | asset_url }}" defer="defer"></script>
  {%- endif -%}

  {% if section.settings.image_zoom == 'hover' %}
    <script id="EnableZoomOnHover-main" src="{{ 'magnify.js' | asset_url }}" defer="defer"></script>
  {% endif %}
  {%- if request.design_mode -%}
    <script src="{{ 'theme-editor.js' | asset_url }}" defer="defer"></script>
  {%- endif -%}

  {%- assign first_3d_model = product.media | where: 'media_type', 'model' | first -%}
  {%- if first_3d_model -%}
    {{ 'component-product-model.css' | asset_url | stylesheet_tag }}
    <link
      id="ModelViewerStyle"
      rel="stylesheet"
      href="https://cdn.shopify.com/shopifycloud/model-viewer-ui/assets/v1.0/model-viewer-ui.css"
      media="print"
      onload="this.media='all'"
    >
    <link
      id="ModelViewerOverride"
      rel="stylesheet"
      href="{{ 'component-model-viewer-ui.css' | asset_url }}"
      media="print"
      onload="this.media='all'"
    >
  {%- endif -%}

  {% assign variant_images = product.images | where: 'attached_to_variant?', true | map: 'src' %}
  <div class="page-width">
    <div class="product product--{{ section.settings.media_size }} product--{{ section.settings.media_position }} product--{{ section.settings.gallery_layout }} product--mobile-{{ section.settings.mobile_thumbnails }} grid grid--1-col {% if product.media.size > 0 %}grid--2-col-tablet{% else %}product--no-media{% endif %} lg:ts-pb-36 md:ts-pb-20 ts-pb-24">
      <div class="grid__item product__media-wrapper">
        {% render 'product-media-gallery', variant_images: variant_images %}
      </div>
      <div class="product__info-wrapper grid__item{% if settings.page_width > 1400 and section.settings.media_size == "small" %} product__info-wrapper--extra-padding{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
        <section
          id="ProductInfo-{{ section.id }}"
          class="product__info-container{% if section.settings.enable_sticky_info %} product__column-sticky{% endif %}"
        >
          {%- assign product_form_id = 'product-form-' | append: section.id -%}

          {%- for block in section.blocks -%}
            {%- case block.type -%}
              {%- when '@app' -%}
                {% render block %}
              {%- when 'text' -%}
                <p
                  class="product__text inline-richtext{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %} ts-text-size_sm ts-text-[rgba(var(--color-foreground))]"
                  {{ block.shopify_attributes }}
                >
                  {{- block.settings.text -}}
                </p>
              {%- when 'title' -%}
                <div class="product__title !ts-mt-0 !ts-mb-4" {{ block.shopify_attributes }}>
                  <h1 class="md:ts-text-size_lg ts-text-[1.8rem] ts-font-black">
                    {{ product.title | escape }}
                  </h1>
                  <a href="{{ product.url }}" class="product__title">
                    <h2 class="h1">
                      {{ product.title | escape }}
                    </h2>
                  </a>
                </div>
              {%- when 'price' -%}
                {%- unless product.tags contains 'coming_soon' -%}
                  <div id="price-{{ section.id }}" class="!ts-mt-4 !ts-mb-5" role="status" {{ block.shopify_attributes }}>
                    {%- render 'price',
                      product: product,
                      use_variant: true,
                      show_badges: true,
                      price_class: 'price--large'
                    -%}
                  </div>
                  {%- if product.quantity_price_breaks_configured? -%}
                    <div class="volume-pricing-note" id="Volume-Note-{{ section.id }}">
                      <span>{{ 'products.product.volume_pricing.note' | t }}</span>
                    </div>
                  {%- endif -%}
                  {%- if cart.taxes_included or cart.duties_included or shop.shipping_policy.body != blank -%}
                    <div class="product__tax caption rte visually-hidden">
                      {%- if cart.duties_included and cart.taxes_included -%}
                        {{ 'products.product.duties_and_taxes_included' | t }}
                      {%- elsif cart.taxes_included -%}
                        {{ 'products.product.taxes_included' | t }}
                      {%- elsif cart.duties_included -%}
                        {{ 'products.product.duties_included' | t }}
                      {%- endif -%}
                      {%- if shop.shipping_policy.body != blank -%}
                        {{ 'products.product.shipping_policy_html' | t: link: shop.shipping_policy.url }}
                      {%- endif -%}
                    </div>
                  {%- endif -%}
                  <div {{ block.shopify_attributes }}>
                    {%- assign product_form_installment_id = 'product-form-installment-' | append: section.id -%}
                    {%- form 'product', product, id: product_form_installment_id, class: 'installment caption-large' -%}
                      <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                      {{ form | payment_terms }}
                    {%- endform -%}
                  </div>
                {%- else -%}
                    Date Time Here
                {%- endunless -%}
              {%- when 'inventory' -%}
                <p
                  class="product__inventory{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}{% if product.selected_or_first_available_variant.inventory_management != 'shopify' %} visibility-hidden{% endif %}"
                  {{ block.shopify_attributes }}
                  id="Inventory-{{ section.id }}"
                  role="status"
                >
                  {%- if product.selected_or_first_available_variant.inventory_management == 'shopify' -%}
                    {%- if product.selected_or_first_available_variant.inventory_quantity > 0 -%}
                      {%- if product.selected_or_first_available_variant.inventory_quantity
                          <= block.settings.inventory_threshold
                      -%}
                        <span class="svg-wrapper" style="color: rgb(238, 148, 65)">
                          {{- 'icon-inventory-status.svg' | inline_asset_content -}}
                        </span>
                        {%- if block.settings.show_inventory_quantity -%}
                          {{-
                            'products.product.inventory_low_stock_show_count'
                            | t: quantity: product.selected_or_first_available_variant.inventory_quantity
                          -}}
                        {%- else -%}
                          {{- 'products.product.inventory_low_stock' | t -}}
                        {%- endif -%}
                      {%- else -%}
                        <span class="svg-wrapper" style="color: rgb(62, 214, 96)">
                          {{- 'icon-inventory-status.svg' | inline_asset_content -}}
                        </span>
                        {%- if block.settings.show_inventory_quantity -%}
                          {{-
                            'products.product.inventory_in_stock_show_count'
                            | t: quantity: product.selected_or_first_available_variant.inventory_quantity
                          -}}
                        {%- else -%}
                          {{- 'products.product.inventory_in_stock' | t -}}
                        {%- endif -%}
                      {%- endif -%}
                    {%- else -%}
                      {%- if product.selected_or_first_available_variant.inventory_policy == 'continue' -%}
                        <span class="svg-wrapper" style="color: rgb(62, 214, 96)">
                          {{- 'icon-inventory-status.svg' | inline_asset_content -}}
                        </span>
                        {{- 'products.product.inventory_out_of_stock_continue_selling' | t -}}
                      {%- else -%}
                        <span class="svg-wrapper" style="color: rgb(200, 200, 200)">
                          {{- 'icon-inventory-status.svg' | inline_asset_content -}}
                        </span>
                        {{- 'products.product.inventory_out_of_stock' | t -}}
                      {%- endif -%}
                    {%- endif -%}
                  {%- endif -%}
                </p>
              {%- when 'description' -%}
                {%- if product.description != blank -%}
                  <div class="product__description rte quick-add-hidden" {{ block.shopify_attributes }}>
                    {{ product.description }}
                  </div>
                {%- endif -%}
              {%- when 'sku' -%}
                <p
                  class="product__sku{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}{% if product.selected_or_first_available_variant.sku.size == 0 %} visibility-hidden{% endif %}"
                  id="Sku-{{ section.id }}"
                  role="status"
                  {{ block.shopify_attributes }}
                >
                  <span class="visually-hidden">{{ 'products.product.sku' | t }}:</span>
                  {{- product.selected_or_first_available_variant.sku -}}
                </p>
              {%- when 'custom_liquid' -%}
                {{ block.settings.custom_liquid }}
              {%- when 'collapsible_tab' -%}
                <div class="product__accordion accordion quick-add-hidden" {{ block.shopify_attributes }}>
                  <details id="Details-{{ block.id }}-{{ section.id }}">
                    <summary>
                      <div class="summary__title">
                        {% render 'icon-accordion', icon: block.settings.icon %}
                        <h2 class="h4 accordion__title inline-richtext">
                          {{ block.settings.heading | default: block.settings.page.title | escape }}
                        </h2>
                      </div>
                      {{- 'icon-caret.svg' | inline_asset_content -}}
                    </summary>
                    <div class="accordion__content rte" id="ProductAccordion-{{ block.id }}-{{ section.id }}">
                      {{ block.settings.content }}
                      {{ block.settings.page.content }}
                    </div>
                  </details>
                </div>
              {%- when 'quantity_selector' -%}
                {%- unless product.tags contains 'coming_soon' -%}
                  <div
                    id="Quantity-Form-{{ section.id }}"
                    class="product-form__input product-form__quantity{% if settings.inputs_shadow_vertical_offset != 0 and settings.inputs_shadow_vertical_offset < 0 %} product-form__quantity-top{% endif %} ts-flex-col"
                    {{ block.shopify_attributes }}
                  >
                    {% comment %} TODO: enable theme-check once `item_count_for_variant` is accepted as valid filter {% endcomment %}
                    {% # theme-check-disable %}
                    {%- assign cart_qty = cart
                      | item_count_for_variant: product.selected_or_first_available_variant.id
                    -%}
                    {% # theme-check-enable %}
                    <label class="quantity__label form__label" for="Quantity-{{ section.id }}">
                      {{ 'products.product.quantity.label' | t }}
                      <span class="quantity__rules-cart{% if cart_qty == 0 %} hidden{% endif %}">
                        {%- render 'loading-spinner' -%}
                        <span
                          >(
                          {{- 'products.product.quantity.in_cart_html' | t: quantity: cart_qty -}}
                          )</span
                        >
                      </span>
                    </label>
                    <div class="price-per-item__container">
                      <quantity-input class="quantity" data-url="{{ product.url }}" data-section="{{ section.id }}">
                        <button class="quantity__button" name="minus" type="button">
                          <span class="visually-hidden">
                            {{- 'products.product.quantity.decrease' | t: product: product.title | escape -}}
                          </span>
                          <span class="svg-wrapper">
                            {{- 'icon-minus.svg' | inline_asset_content -}}
                          </span>
                        </button>
                        <input
                          class="quantity__input"
                          type="number"
                          name="quantity"
                          id="Quantity-{{ section.id }}"
                          data-cart-quantity="{{ cart_qty }}"
                          data-min="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                          min="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                          {% if product.selected_or_first_available_variant.quantity_rule.max != null %}
                            data-max="{{ product.selected_or_first_available_variant.quantity_rule.max }}"
                            max="{{ product.selected_or_first_available_variant.quantity_rule.max }}"
                          {% endif %}
                          step="{{ product.selected_or_first_available_variant.quantity_rule.increment }}"
                          value="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                          form="{{ product_form_id }}"
                        >
                        <button class="quantity__button" name="plus" type="button">
                          <span class="visually-hidden">
                            {{- 'products.product.quantity.increase' | t: product: product.title | escape -}}
                          </span>
                          <span class="svg-wrapper">
                            {{- 'icon-plus.svg' | inline_asset_content -}}
                          </span>
                        </button>
                      </quantity-input>
                      {%- liquid
                        assign volume_pricing_array = product.selected_or_first_available_variant.quantity_price_breaks | sort: 'quantity' | reverse
                        assign current_qty_for_volume_pricing = cart_qty | plus: product.selected_or_first_available_variant.quantity_rule.min
                        if cart_qty > 0
                          assign current_qty_for_volume_pricing = cart_qty | plus: product.selected_or_first_available_variant.quantity_rule.increment
                        endif
                      -%}
                      {%- if product.quantity_price_breaks_configured? -%}
                        <price-per-item
                          id="Price-Per-Item-{{ section.id }}"
                          data-section-id="{{ section.id }}"
                          data-variant-id="{{ product.selected_or_first_available_variant.id }}"
                        >
                          {%- if product.selected_or_first_available_variant.quantity_price_breaks.size > 0 -%}
                            {%- assign variant_price_compare = product.selected_or_first_available_variant.compare_at_price -%}
                            <div class="price-per-item">
                              {%- if variant_price_compare -%}
                                <dl class="price-per-item--current">
                                  <dt class="visually-hidden">
                                    {{ 'products.product.price.regular_price' | t }}
                                  </dt>
                                  <dd>
                                    <s class="variant-item__old-price">
                                      {{ variant_price_compare | money_with_currency }}
                                    </s>
                                  </dd>
                                </dl>
                              {%- endif -%}
                              {%- if current_qty_for_volume_pricing < volume_pricing_array.last.minimum_quantity -%}
                                {%- assign variant_price = product.selected_or_first_available_variant.price
                                  | money_with_currency
                                -%}
                                <span class="price-per-item--current">
                                  {{- 'products.product.volume_pricing.price_at_each_html' | t: price: variant_price -}}
                                </span>
                              {%- else -%}
                                {%- for price_break in volume_pricing_array -%}
                                  {%- if current_qty_for_volume_pricing >= price_break.minimum_quantity -%}
                                    {%- assign price_break_price = price_break.price | money_with_currency -%}
                                    <span class="price-per-item--current">
                                      {{-
                                        'products.product.volume_pricing.price_at_each_html'
                                        | t: price: price_break_price
                                      -}}
                                    </span>
                                    {%- break -%}
                                  {%- endif -%}
                                {%- endfor -%}
                              {%- endif -%}
                            </div>
                          {%- else -%}
                            {%- assign variant_price = product.selected_or_first_available_variant.price
                              | money_with_currency
                            -%}
                            {%- assign variant_price_compare = product.selected_or_first_available_variant.compare_at_price -%}
                            <div class="price-per-item">
                              {%- if variant_price_compare -%}
                                <dl class="price-per-item--current">
                                  <dt class="visually-hidden">
                                    {{ 'products.product.price.regular_price' | t }}
                                  </dt>
                                  <dd>
                                    <s class="variant-item__old-price">
                                      {{ variant_price_compare | money_with_currency }}
                                    </s>
                                  </dd>
                                  <dt class="visually-hidden">
                                    {{ 'products.product.price.sale_price' | t }}
                                  </dt>
                                  <dd>
                                    <span class="price-per-item--current">
                                      {{- 'products.product.volume_pricing.price_at_each_html' | t: price: variant_price -}}
                                    </span>
                                  </dd>
                                </dl>
                              {%- else -%}
                                <span class="price-per-item--current">
                                  {{- 'products.product.volume_pricing.price_at_each_html' | t: price: variant_price -}}
                                </span>
                              {%- endif -%}
                            </div>
                          {%- endif -%}
                        </price-per-item>
                      {%- endif -%}
                    </div>
                    <div class="quantity__rules caption" id="Quantity-Rules-{{ section.id }}">
                      {%- if product.selected_or_first_available_variant.quantity_rule.increment > 1 -%}
                        <span class="divider">
                          {{-
                            'products.product.quantity.multiples_of'
                            | t: quantity: product.selected_or_first_available_variant.quantity_rule.increment
                          -}}
                        </span>
                      {%- endif -%}
                      {%- if product.selected_or_first_available_variant.quantity_rule.min > 1 -%}
                        <span class="divider">
                          {{-
                            'products.product.quantity.minimum_of'
                            | t: quantity: product.selected_or_first_available_variant.quantity_rule.min
                          -}}
                        </span>
                      {%- endif -%}
                      {%- if product.selected_or_first_available_variant.quantity_rule.max != null -%}
                        <span class="divider">
                          {{-
                            'products.product.quantity.maximum_of'
                            | t: quantity: product.selected_or_first_available_variant.quantity_rule.max
                          -}}
                        </span>
                      {%- endif -%}
                    </div>
                    {%- if product.quantity_price_breaks_configured? -%}
                      <volume-pricing class="parent-display" id="Volume-{{ section.id }}">
                        {%- if product.selected_or_first_available_variant.quantity_price_breaks.size > 0 -%}
                          <span class="caption-large">{{ 'products.product.volume_pricing.title' | t }}</span>
                          <ul class="list-unstyled">
                            <li>
                              <span>{{ product.selected_or_first_available_variant.quantity_rule.min }}+</span>
                              {%- assign price = product.selected_or_first_available_variant.price
                                | money_with_currency
                              -%}
                              <span data-text="{{ 'products.product.volume_pricing.price_at_each_html' | t: price: variant_price }}">
                                {{- 'sections.quick_order_list.each' | t: money: price -}}
                              </span>
                            </li>
                            {%- for price_break in product.selected_or_first_available_variant.quantity_price_breaks -%}
                              {%- assign price_break_price = price_break.price | money_with_currency -%}
                              <li class="{%- if forloop.index >= 3 -%}show-more-item hidden{%- endif -%}">
                                <span>
                                  {{- price_break.minimum_quantity -}}
                                  <span aria-hidden="true">+</span></span
                                >
                                <span data-text="{{ 'products.product.volume_pricing.price_at_each_html' | t: price: price_break_price }}">
                                  {{- 'sections.quick_order_list.each' | t: money: price_break_price -}}
                                </span>
                              </li>
                            {%- endfor -%}
                          </ul>
                          {%- if product.selected_or_first_available_variant.quantity_price_breaks.size >= 3 -%}
                            <show-more-button>
                              <button
                                class="button-show-more link underlined-link"
                                id="Show-More-{{ section.id }}"
                                type="button"
                              >
                                <span class="label-show-more label-text"
                                  ><span aria-hidden="true">+ </span>{{ 'products.facets.show_more' | t }}
                                </span>
                              </button>
                            </show-more-button>
                          {%- endif -%}
                        {%- endif -%}
                      </volume-pricing>
                    {%- endif -%}
                  </div>
                {%- endunless -%}
              {%- when 'popup' -%}
                <modal-opener
                  class="product-popup-modal__opener quick-add-hidden"
                  data-modal="#PopupModal-{{ block.id }}"
                  {{ block.shopify_attributes }}
                >
                  <button
                    id="ProductPopup-{{ block.id }}"
                    class="product-popup-modal__button link"
                    type="button"
                    aria-haspopup="dialog"
                  >
                    {{ block.settings.text | default: block.settings.page.title | escape }}
                  </button>
                </modal-opener>
              {%- when 'share' -%}
                {% liquid
                  assign share_url = product.selected_variant.url | default: product.url | prepend: request.origin
                  render 'share-button', block: block, share_link: share_url
                %}

              {%- when 'variant_picker' -%}
                {%- unless product.tags contains 'coming_soon' -%}
                  {% render 'product-variant-picker', product: product, block: block, product_form_id: product_form_id %}
                {%- endunless -%}
              {%- when 'buy_buttons' -%}
                {%- render 'buy-buttons',
                  block: block,
                  product: product,
                  product_form_id: product_form_id,
                  section_id: section.id,
                  show_pickup_availability: false
                -%}
              {%- when 'rating' -%}
                {%- if product.metafields.reviews.rating.value != blank -%}
                  <div class="rating-wrapper">
                    {% liquid
                      assign rating_decimal = 0
                      assign decimal = product.metafields.reviews.rating.value.rating | modulo: 1
                      if decimal >= 0.3 and decimal <= 0.7
                        assign rating_decimal = 0.5
                      elsif decimal > 0.7
                        assign rating_decimal = 1
                      endif
                    %}
                    <div
                      class="rating"
                      role="img"
                      aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: product.metafields.reviews.rating.value, rating_max: product.metafields.reviews.rating.value.scale_max }}"
                    >
                      <span
                        aria-hidden="true"
                        class="rating-star"
                        style="--rating: {{ product.metafields.reviews.rating.value.rating | floor }}; --rating-max: {{ product.metafields.reviews.rating.value.scale_max }}; --rating-decimal: {{ rating_decimal }};"
                      ></span>
                    </div>
                    <p class="rating-text caption">
                      <span aria-hidden="true">
                        {{- product.metafields.reviews.rating.value }} /
                        {{ product.metafields.reviews.rating.value.scale_max -}}
                      </span>
                    </p>
                    <p class="rating-count caption">
                      <span aria-hidden="true">({{ product.metafields.reviews.rating_count }})</span>
                      <span class="visually-hidden">
                        {{- product.metafields.reviews.rating_count }}
                        {{ 'accessibility.total_reviews' | t -}}
                      </span>
                    </p>
                  </div>
                {%- endif -%}
              {%- when 'complementary' -%}
                <product-recommendations
                  class="complementary-products quick-add-hidden{% if block.settings.make_collapsible_row %} is-accordion{% endif %}{% if block.settings.enable_quick_add %} complementary-products-contains-quick-add{% endif %} ts-block !ts-mt-8"
                  data-url="{{ routes.product_recommendations_url }}?limit={{ block.settings.product_list_limit }}&intent=complementary"
                  data-section-id="{{ section.id }}"
                  data-product-id="{{ product.id }}"
                >
                  {%- if recommendations.performed and recommendations.products_count > 0 -%}
                    <div class="complementary-products__container" aria-label="{{ 'accessibility.complementary_products' | t }}" {{ block.shopify_attributes -}}>
                      <slideshow-component>
                        <div class="complementary-products--grid ts-grid ts-grid-cols-4 ts-gap-px">
                          {%- for product in recommendations.products limit: block.settings.products_per_page -%}
                            {% render 'mw_complementary_product_card', product: product %}
                          {%- endfor -%}
                        </div>
  
                        <div class="complementary-products--grid ts-grid ts-grid-rows-[0fr] ts-transition-[grid-template-rows] ts-duration-300 is-active:ts-grid-rows-[1fr]" data-more-products>
                          <div class="complementary-products--wrapper ts-grid ts-grid-cols-4 ts-gap-px ts-overflow-hidden">
                            {%- for product in recommendations.products offset: continue -%}
                              {% render 'mw_complementary_product_card', product: product %}
                            {%- endfor -%}
                          </div>
                        </div>
                        <button data-related-products-toggle class="button--full-width ts-bg-gray ts-group ts-justify-center ts-py-[1.125rem] is-active:ts-mt-px ts-text-size_sm ts-text-[rgba(var(--color-foreground))] hover:!ts-text-[rgba(var(--color-foreground))]">
                          <span class="group-[.ts-is-active]:ts-hidden">See more styles</span>
                          <span class="ts-hidden group-[.ts-is-active]:ts-block">Less styles</span>
                        </button>
                      </slideshow-component>
                    </div>
                  {%- endif -%}
                </product-recommendations>
              {%- when 'icon-with-text' -%}
                {% render 'icon-with-text', block: block %}
            {%- endcase -%}
          {%- endfor -%}
          <a href="{{ product.url }}" class="link product__view-details animate-arrow">
            {{ 'products.product.view_full_details' | t }}
            {{- 'icon-arrow.svg' | inline_asset_content -}}
          </a>
        </section>
      </div>
    </div>

    <div class="ts-grid lg:ts-grid-cols-12 ts-grid-cols-1 lg:ts-border-t lg:ts-border-solid lg:ts-border-gray lg:-ts-mx-8 mobile:ts-space-y-32">
      <div class="product-main-descriptions lg:ts-col-span-7 lg:ts-py-[clamp(2rem,5vw,8rem)] lg:ts-px-[clamp(2rem,6vw,10rem)]{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
        <div class="descriptions-wrapper ts-w-full ts-max-w-[70rem] ts-mx-auto">
          <div class="rte md:ts-text-size_lg ts-text-size_md">
            {{ product.description }}
          </div>
        </div>
      </div>
      <div class="product-extra-details lg:ts-col-span-5 lg:ts-border-l lg:ts-border-solid lg:ts-border-gray lg:ts-py-[clamp(2rem,5vw,8rem)] lg:ts-px-[clamp(2rem,6vw,10rem)] mobile:ts-pb-24 md:ts-space-y-20 ts-space-y-14">
        {%- if product.metafields.data.highlights != blank -%}
          <div class="extra-detail highlights-detail ts-text-size_md ts-flex md:ts-flex-row ts-flex-col ts-items-start md:ts-gap-8 ts-gap-4{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
            <div class="extra-detail-label ts-font-medium md:ts-basis-[15rem]">
              <span>{{ 'products.product.highlights' | t }}</span>
            </div>
            <div class="extra-detail-content ts-flex-1">
              <div class="rte">
                {{ product.metafields.data.highlights | metafield_tag }}

                {%- if product.metafields.data.final_sale -%}
                  <p class="ts-font-medium">{{ 'products.product.final_sale' | t }}</p>
                {%- endif -%}
              </div>
            </div>
          </div>
        {%- endif -%}

        {%- if product.metafields.data.sizing != blank -%}
          <div class="extra-detail sizing-detail ts-text-size_md ts-flex md:ts-flex-row ts-flex-col ts-items-start md:ts-gap-8 ts-gap-4{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
            <div class="extra-detail-label ts-font-medium md:ts-basis-[15rem]">
              <span>{{ 'products.product.sizing' | t }}</span>
            </div>
            <div class="extra-detail-content ts-flex-1">
              <div class="rte">
                <p>{{ product.metafields.data.sizing | metafield_text }}</p>
              </div>
            </div>
          </div>
        {%- endif -%}

        {%- if section.settings.show_store_info and section.settings.store_info != blank -%}
          <div class="extra-detail store-info-detail ts-text-size_md ts-flex md:ts-flex-row ts-flex-col ts-items-start md:ts-gap-8 ts-gap-4{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
            <div class="extra-detail-label ts-font-medium md:ts-basis-[15rem]">
              <span>{{ 'products.product.store_info' | t }}</span>
            </div>
            <div class="extra-detail-content ts-flex-1">
              <div class="rte">
                {{ section.settings.store_info }}
              </div>
            </div>
          </div>
        {%- endif -%}

        {%- if section.settings.show_delevery_info and section.settings.delevery_info != blank -%}
          <div class="extra-detail delivery-info-detail ts-text-size_md ts-flex md:ts-flex-row ts-flex-col ts-items-start md:ts-gap-8 ts-gap-4{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
            <div class="extra-detail-label ts-font-medium md:ts-basis-[15rem]">
              <span>{{ 'products.product.delivery_returns' | t }}</span>
            </div>
            <div class="extra-detail-content ts-flex-1">
              <div class="rte">
                {{ section.settings.delevery_info }}
              </div>
            </div>
          </div>
        {%- endif -%}

        <accordion-element class="ts-block">
          {%- for block in section.blocks -%}
            {%- if block.type == 'accordion' -%}
              {%- assign item = block.settings -%}

              {%- if item.accordion_title != blank and item.accordion_content != blank -%}
                <accordion-item class="ts-block ts-transition-all ts-duration-300{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %} ts-border-0 ts-border-b ts-border-solid ts-border-gray ts-pb-4"{% if settings.animations_reveal_on_scroll %} data-cascade style="--animation-order: {{ forloop.index  }};"{% endif %} {{ block.shopify_attributes }}>
                  <div class="accordion-header ts-font-medium ts-text-size_sm ts-cursor-pointer ts-uppercase ts-select-none{%  unless forloop.first %} ts-pt-[1.3rem]{% endunless %} ts-flex ts-items-center ts-justify-between" slot="header">
										<span>{{ item.accordion_title }}</span>
                    <span class="svg-icon-plus ts-block *:ts-w-4 *:ts-h-4">{{- 'icon-plus.svg' | inline_asset_content -}}</span>
                    <span class="svg-icon-minus ts-hidden *:ts-w-4 *:ts-h-4">{{- 'icon-minus.svg' | inline_asset_content -}}</span>
									</div>
									<div class="accordion-content ts-mt-2 ts-max-h-0 ts-overflow-hidden ts-text-size_md">
                    <div class="rte">
                      {{ item.accordion_content }}
                    </div>
									</div>
                </accordion-item>
              {%- endif -%}
            {%- endif -%}
          {%- endfor -%}
        </accordion-element>
      </div>
    </div>

    {% render 'product-media-modal', variant_images: variant_images %}

    {% assign popups = section.blocks | where: 'type', 'popup' %}
    {%- for block in popups -%}
      <modal-dialog id="PopupModal-{{ block.id }}" class="product-popup-modal" {{ block.shopify_attributes }}>
        <div
          role="dialog"
          aria-label="{{ block.settings.text }}"
          aria-modal="true"
          class="product-popup-modal__content"
          tabindex="-1"
        >
          <button
            id="ModalClose-{{ block.id }}"
            type="button"
            class="product-popup-modal__toggle"
            aria-label="{{ 'accessibility.close' | t }}"
          >
            {{- 'icon-close.svg' | inline_asset_content -}}
          </button>
          <div class="product-popup-modal__content-info">
            <h1 class="h2">{{ block.settings.page.title | escape }}</h1>
            {{ block.settings.page.content }}
          </div>
        </div>
      </modal-dialog>
    {%- endfor -%}

    {%- if product.media.size > 0 -%}
      <script src="{{ 'product-modal.js' | asset_url }}" defer="defer"></script>
      <script src="{{ 'media-gallery.js' | asset_url }}" defer="defer"></script>
      <script src="{{ 'mw_product_media_modal_slider.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}

    {%- if first_3d_model -%}
      <script type="application/json" id="ProductJSON-{{ product.id }}">
        {{ product.media | where: 'media_type', 'model' | json }}
      </script>
      <script src="{{ 'product-model.js' | asset_url }}" defer></script>
    {%- endif -%}

    {%- liquid
      if product.selected_or_first_available_variant.featured_media
        assign seo_media = product.selected_or_first_available_variant.featured_media
      else
        assign seo_media = product.featured_media
      endif
    -%}

    <script type="application/ld+json">
      {{ product | structured_data }}
    </script>
  </div>

  {%- if product.metafields.data.featured_images != blank -%}
    <div class="product-featured-images ts-grid md:ts-grid-cols-2 ts-grid-cols-1{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
      {%- for image in product.metafields.data.featured_images.value -%}
        {% render 'mw_image', image: image, class: ' product-featured-images__item', section: section %}
      {%- endfor -%}
    </div>
  {%- endif -%}
</product-info>

{% schema %}
{
  "name": "t:sections.main-product.name",
  "tag": "section",
  "class": "section",
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "text",
      "name": "t:sections.main-product.blocks.text.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "text",
          "default": "t:sections.main-product.blocks.text.settings.text.default",
          "label": "t:sections.main-product.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.text.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.text.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.main-product.blocks.text.settings.text_style.label"
        }
      ]
    },
    {
      "type": "title",
      "name": "t:sections.main-product.blocks.title.name",
      "limit": 1
    },
    {
      "type": "price",
      "name": "t:sections.main-product.blocks.price.name",
      "limit": 1
    },
    {
      "type": "sku",
      "name": "t:sections.main-product.blocks.sku.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.sku.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.sku.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.sku.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.main-product.blocks.sku.settings.text_style.label"
        }
      ]
    },
    {
      "type": "inventory",
      "name": "t:sections.main-product.blocks.inventory.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.main-product.blocks.inventory.settings.text_style.label"
        },
        {
          "type": "range",
          "id": "inventory_threshold",
          "label": "t:sections.main-product.blocks.inventory.settings.inventory_threshold.label",
          "min": 0,
          "max": 100,
          "step": 1,
          "default": 10
        },
        {
          "type": "checkbox",
          "id": "show_inventory_quantity",
          "label": "t:sections.main-product.blocks.inventory.settings.show_inventory_quantity.label",
          "default": true
        }
      ]
    },
    {
      "type": "quantity_selector",
      "name": "t:sections.main-product.blocks.quantity_selector.name",
      "limit": 1
    },
    {
      "type": "variant_picker",
      "name": "t:sections.main-product.blocks.variant_picker.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "picker_type",
          "options": [
            {
              "value": "dropdown",
              "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__1.label"
            },
            {
              "value": "button",
              "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__2.label"
            }
          ],
          "default": "button",
          "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.label"
        },
        {
          "id": "swatch_shape",
          "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.label",
          "type": "select",
          "info": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.info",
          "options": [
            {
              "value": "circle",
              "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.options__1.label"
            },
            {
              "value": "square",
              "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.options__2.label"
            },
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.options__3.label"
            }
          ],
          "default": "circle"
        }
      ]
    },
    {
      "type": "buy_buttons",
      "name": "t:sections.main-product.blocks.buy_buttons.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_dynamic_checkout",
          "default": true,
          "label": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.label",
          "info": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.info"
        },
        {
          "type": "checkbox",
          "id": "show_gift_card_recipient",
          "default": true,
          "label": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.label",
          "info": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.info"
        }
      ]
    },
    {
      "type": "description",
      "name": "t:sections.main-product.blocks.description.name",
      "limit": 1
    },
    {
      "type": "share",
      "name": "t:sections.main-product.blocks.share.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "share_label",
          "label": "t:sections.main-product.blocks.share.settings.text.label",
          "default": "t:sections.main-product.blocks.share.settings.text.default"
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.custom-liquid.name",
      "settings": [
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.custom-liquid.settings.custom_liquid.label",
          "info": "t:sections.custom-liquid.settings.custom_liquid.info"
        }
      ]
    },
    {
      "type": "collapsible_tab",
      "name": "t:sections.main-product.blocks.collapsible_tab.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "t:sections.main-product.blocks.collapsible_tab.settings.heading.default",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.heading.label"
        },
        {
          "type": "select",
          "id": "icon",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "check_mark",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.label"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.content.label"
        },
        {
          "type": "page",
          "id": "page",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.page.label"
        }
      ]
    },
    {
      "type": "popup",
      "name": "t:sections.main-product.blocks.popup.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "default": "t:sections.main-product.blocks.popup.settings.link_label.default",
          "label": "t:sections.main-product.blocks.popup.settings.link_label.label"
        },
        {
          "id": "page",
          "type": "page",
          "label": "t:sections.main-product.blocks.popup.settings.page.label"
        }
      ]
    },
    {
      "type": "rating",
      "name": "t:sections.main-product.blocks.rating.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.rating.settings.paragraph.content"
        }
      ]
    },
    {
      "type": "complementary",
      "name": "t:sections.main-product.blocks.complementary_products.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.complementary_products.settings.paragraph.content"
        },
        {
          "type": "text",
          "id": "block_heading",
          "default": "t:sections.main-product.blocks.complementary_products.settings.heading.default",
          "label": "t:sections.main-product.blocks.complementary_products.settings.heading.label"
        },
        {
          "type": "checkbox",
          "id": "make_collapsible_row",
          "default": false,
          "label": "t:sections.main-product.blocks.complementary_products.settings.make_collapsible_row.label"
        },
        {
          "type": "select",
          "id": "icon",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "price_tag",
          "info": "t:sections.main-product.blocks.complementary_products.settings.icon.info",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.label"
        },
        {
          "type": "range",
          "id": "product_list_limit",
          "min": 1,
          "max": 10,
          "step": 1,
          "default": 10,
          "label": "t:sections.main-product.blocks.complementary_products.settings.product_list_limit.label"
        },
        {
          "type": "range",
          "id": "products_per_page",
          "min": 1,
          "max": 4,
          "step": 1,
          "default": 3,
          "label": "t:sections.main-product.blocks.complementary_products.settings.products_per_page.label"
        },
        {
          "type": "select",
          "id": "pagination_style",
          "options": [
            {
              "value": "dots",
              "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.options.option_1"
            },
            {
              "value": "counter",
              "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.options.option_2"
            },
            {
              "value": "numbers",
              "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.options.option_3"
            }
          ],
          "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.label",
          "default": "counter"
        },
        {
          "type": "header",
          "content": "t:sections.main-product.blocks.complementary_products.settings.product_card.heading"
        },
        {
          "type": "select",
          "id": "image_ratio",
          "options": [
            {
              "value": "portrait",
              "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.options.option_1"
            },
            {
              "value": "square",
              "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.options.option_2"
            }
          ],
          "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.label",
          "default": "square"
        },
        {
          "type": "checkbox",
          "id": "enable_quick_add",
          "label": "t:sections.main-product.blocks.complementary_products.settings.enable_quick_add.label",
          "default": false
        }
      ]
    },
    {
      "type": "icon-with-text",
      "name": "t:sections.main-product.blocks.icon_with_text.name",
      "settings": [
        {
          "type": "select",
          "id": "layout",
          "options": [
            {
              "value": "horizontal",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__1.label"
            },
            {
              "value": "vertical",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__2.label"
            }
          ],
          "default": "horizontal",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.label"
        },
        {
          "type": "header",
          "content": "t:sections.main-product.blocks.icon_with_text.settings.pairing_1.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.pairing_1.info"
        },
        {
          "type": "select",
          "id": "icon_1",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "heart",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_1.label"
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_1.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading_1",
          "default": "t:sections.main-product.blocks.icon_with_text.settings.heading_1.default",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.heading_1.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.heading.info"
        },
        {
          "type": "header",
          "content": "t:sections.main-product.blocks.icon_with_text.settings.pairing_2.label"
        },        
        {
          "type": "select",
          "id": "icon_2",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "return",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_2.label"
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_2.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading_2",
          "default": "t:sections.main-product.blocks.icon_with_text.settings.heading_2.default",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.heading_2.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.heading.info"
        },
        {
          "type": "header",
          "content": "t:sections.main-product.blocks.icon_with_text.settings.pairing_3.label"
        },        
        {
          "type": "select",
          "id": "icon_3",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "truck",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_3.label"
        },
        {
          "type": "image_picker",
          "id": "image_3",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_3.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading_3",
          "default": "t:sections.main-product.blocks.icon_with_text.settings.heading_3.default",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.heading_3.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.heading.info"
        }
      ]
    },
    {
      "type": "accordion",
      "name": "Accordion Item",
      "limit": 10,
      "settings": [
        {
          "type": "text",
          "id": "accordion_title",
          "label": "Accordion Title",
          "default": "Accordion Title"
        },
        {
          "type": "richtext",
          "id": "accordion_content",
          "label": "Accordion Content",
          "default": "<p>Accordion Content</p>"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "enable_sticky_info",
      "default": true,
      "label": "t:sections.main-product.settings.enable_sticky_info.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.main-product.settings.header.content"
    },
    {
      "type": "select",
      "id": "media_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.main-product.settings.media_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.main-product.settings.media_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.main-product.settings.media_size.options__3.label"
        }
      ],
      "default": "large",
      "label": "t:sections.main-product.settings.media_size.label"
    },
    {
      "type": "checkbox",
      "id": "constrain_to_viewport",
      "default": true,
      "label": "t:sections.main-product.settings.constrain_to_viewport.label"
    },
    {
      "type": "select",
      "id": "media_fit",
      "options": [
        {
          "value": "contain",
          "label": "t:sections.main-product.settings.media_fit.options__1.label"
        },
        {
          "value": "cover",
          "label": "t:sections.main-product.settings.media_fit.options__2.label"
        }
      ],
      "default": "contain",
      "label": "t:sections.main-product.settings.media_fit.label"
    },
    {
      "type": "select",
      "id": "gallery_layout",
      "options": [
        {
          "value": "stacked",
          "label": "t:sections.main-product.settings.gallery_layout.options__1.label"
        },
        {
          "value": "columns",
          "label": "t:sections.main-product.settings.gallery_layout.options__2.label"
        },
        {
          "value": "thumbnail",
          "label": "t:sections.main-product.settings.gallery_layout.options__3.label"
        },
        {
          "value": "thumbnail_slider",
          "label": "t:sections.main-product.settings.gallery_layout.options__4.label"
        }
      ],
      "default": "stacked",
      "label": "t:sections.main-product.settings.gallery_layout.label"
    },
    {
      "type": "select",
      "id": "mobile_thumbnails",
      "options": [
        {
          "value": "columns",
          "label": "t:sections.main-product.settings.mobile_thumbnails.options__1.label"
        },
        {
          "value": "show",
          "label": "t:sections.main-product.settings.mobile_thumbnails.options__2.label"
        },
        {
          "value": "hide",
          "label": "t:sections.main-product.settings.mobile_thumbnails.options__3.label"
        }
      ],
      "default": "hide",
      "label": "t:sections.main-product.settings.mobile_thumbnails.label"
    },    
    {
      "type": "select",
      "id": "media_position",
      "options": [
        {
          "value": "left",
          "label": "t:sections.main-product.settings.media_position.options__1.label"
        },
        {
          "value": "right",
          "label": "t:sections.main-product.settings.media_position.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.main-product.settings.media_position.label"
    },
    {
      "type": "select",
      "id": "image_zoom",
      "options": [
        {
          "value": "lightbox",
          "label": "t:sections.main-product.settings.image_zoom.options__1.label"
        },
        {
          "value": "hover",
          "label": "t:sections.main-product.settings.image_zoom.options__2.label"
        },
        {
          "value": "none",
          "label": "t:sections.main-product.settings.image_zoom.options__3.label"
        }
      ],
      "default": "lightbox",
      "label": "t:sections.main-product.settings.image_zoom.label"
    },
    {
      "type": "checkbox",
      "id": "hide_variants",
      "default": false,
      "label": "t:sections.main-product.settings.hide_variants.label"
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "default": false,
      "label": "t:sections.main-product.settings.enable_video_looping.label"
    },
    {
      "type": "header",
      "content": "Aditional Informations"
    },
    {
      "type": "checkbox",
      "id": "show_store_info",
      "default": true,
      "label": "Show Store Information"
    },
    {
      "type": "richtext",
      "id": "store_info",
      "label": "Store Information",
      "default": "<p>Store information</p>"
    },
    {
      "type": "checkbox",
      "id": "show_delevery_info",
      "default": true,
      "label": "Show Delivery & Returns"
    },
    {
      "type": "richtext",
      "id": "delevery_info",
      "label": "Delivery & Returns",
      "default": "<p>Delivery informations</p>"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ]
}
{% endschema %}
