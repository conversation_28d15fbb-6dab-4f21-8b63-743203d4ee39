# Countdown Timer Setup Guide

This guide explains how to set up the countdown timer for products in your Shopify store.

## Overview

The countdown timer web component displays a countdown to a specific date and time. When the countdown reaches zero, it automatically reloads the page to show the product pricing, variant picker, quantity selector, and add to cart button.

## Setup Instructions

### 1. Create the Metafield

You need to create a product metafield to store the countdown date and time:

1. Go to **Settings > Metafields** in your Shopify admin
2. Click **Add definition**
3. Set up the metafield with these details:
   - **Namespace and key**: `data.go_live_date`
   - **Name**: `Go Live Date`
   - **Description**: `Date and time when the product becomes available for purchase`
   - **Type**: `Date and time`
   - **Validation**: Set minimum date to today (optional)

### 2. Configure Products

For each product that should show a countdown timer:

1. Go to **Products** in your Shopify admin
2. Edit the product
3. Add the tag `coming_soon` to the product
4. In the metafields section, set the **Go Live Date** to your desired date and time
5. Save the product

### 3. How It Works

The system uses **Liquid server-side logic** to determine what to show:

**Before Go-Live Date:**
- If product has `coming_soon` tag AND current time < go_live_date
- Shows countdown timer instead of purchase options
- Hides price, quantity selector, variant picker, and buy buttons

**After Go-Live Date:**
- If current time >= go_live_date (or no go_live_date set)
- Shows normal product purchase options
- Price, variants, quantity, and add to cart are all visible

**When Countdown Expires:**
- Timer shows "Now Available!" message
- Page reloads automatically after 2 seconds
- Liquid logic detects time has passed and shows purchase options

### 4. Fallback Behavior

If a product has the `coming_soon` tag but no `go_live_date` metafield:
- A generic "Coming Soon" message will be displayed instead of the countdown timer

### 5. Styling

The countdown timer includes:
- **Theme Integration**: Automatically uses your theme's color scheme (button colors, text colors, shadows)
- **Responsive Design**: Works perfectly on all screen sizes (desktop, tablet, mobile)
- **Interactive Elements**: Subtle hover effects and animations
- **Loading States**: Smooth transitions when countdown expires
- **Consistent Branding**: Matches your store's visual identity

### 6. Technical Details

**Files Added:**
- `assets/countdown-timer.js` - Web component JavaScript
- `assets/component-countdown-timer.css` - Styling

**Files Modified:**
- `sections/main-product.liquid` - Added countdown timer integration

**Metafield Used:**
- `product.metafields.data.go_live_date` - Date and time type metafield

### 7. Customization

The countdown timer automatically adapts to your theme's color scheme, but you can further customize it by:

**Color Customization:**
- The timer uses your theme's button colors (`--color-button`, `--color-button-text`)
- Loading state uses secondary button colors
- All shadows and borders use your theme's color variables
- No manual color changes needed - it adapts automatically!

**Advanced Customization:**
- Modify layout and spacing in `assets/component-countdown-timer.css`
- Update messages and behavior in `assets/countdown-timer.js`
- Adjust animations and transitions
- Change border radius and shadow effects

The countdown timer is built as a web component, making it reusable and easy to maintain.

### 8. Testing

A demo file `countdown-timer-demo.html` is included to test the countdown timer functionality:

1. Open the demo file in a web browser
2. Test different countdown scenarios
3. Verify the timer updates correctly
4. Check the expiration behavior

### 9. Troubleshooting

**Common Issues:**

- **Timer not showing**: Ensure the product has both the `coming_soon` tag and the `countdown_date` metafield set
- **Invalid date error**: Check that the metafield date format is correct in Shopify admin
- **Purchase options not appearing after countdown**: The system now automatically shows purchase elements without page reload
- **Elements still hidden after expiration**: Check browser console for JavaScript errors
- **Styling issues**: Verify that `component-countdown-timer.css` is properly loaded
- **JavaScript errors**: Check browser console for any script loading issues

**Date Format Requirements:**
- Use Shopify's date and time metafield type
- The system automatically formats the date for JavaScript compatibility
- Timezone information is preserved from your store settings
