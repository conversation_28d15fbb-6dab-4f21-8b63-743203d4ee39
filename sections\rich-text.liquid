{{ 'section-rich-text.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="isolate{% unless section.settings.full_width %} page-width{% endunless %}">
  <div class="rich-text content-container color-{{ section.settings.color_scheme }} gradient{% if section.settings.full_width %} rich-text--full-width content-container--full-width{% endif %} section-{{ section.id }}-padding">
    <div class="rich-text__wrapper rich-text__wrapper--{{ section.settings.desktop_content_position }}{% if section.settings.full_width %} page-width{% endif %}">
      <div class="rich-text__blocks {{ section.settings.content_alignment }}">
        {%- for block in section.blocks -%}
          {%- case block.type -%}
            {%- when 'heading' -%}
              <h2
                class="rich-text__heading rte inline-richtext {{ block.settings.heading_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %} ts-font-black"
                {{ block.shopify_attributes }}
                {% if settings.animations_reveal_on_scroll %}
                  data-cascade
                  style="--animation-order: {{ forloop.index }};"
                {% endif %}
              >
                {{ block.settings.heading }}
              </h2>
            {%- when 'caption' -%}
              <p
                class="rich-text__caption {{ block.settings.text_style }} {{ block.settings.text_style }}--{{ block.settings.text_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                {{ block.shopify_attributes }}
                {% if settings.animations_reveal_on_scroll %}
                  data-cascade
                  style="--animation-order: {{ forloop.index }};"
                {% endif %}
              >
                {{ block.settings.caption | escape }}
              </p>
            {%- when 'text' -%}
              <div
                class="rich-text__text rte{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %} ts-text-size_sm"
                {{ block.shopify_attributes }}
                {% if settings.animations_reveal_on_scroll %}
                  data-cascade
                  style="--animation-order: {{ forloop.index }};"
                {% endif %}
              >
                {{ block.settings.text }}
              </div>
            {%- when 'button' -%}
              <div
                class="rich-text__buttons{% if block.settings.button_label != blank and block.settings.button_label_2 != blank %} rich-text__buttons--multiple{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                {{ block.shopify_attributes }}
                {% if settings.animations_reveal_on_scroll %}
                  data-cascade
                  style="--animation-order: {{ forloop.index }};"
                {% endif %}
              >
                {%- if block.settings.button_label != blank -%}
                  <a
                    {% if block.settings.button_link == blank %}
                      role="link" aria-disabled="true"
                    {% else %}
                      href="{{ block.settings.button_link }}"
                    {% endif %}
                    class="button{% if block.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}"
                  >
                    {{- block.settings.button_label | escape -}}
                  </a>
                {%- endif -%}
                {%- if block.settings.button_label_2 != blank -%}
                  <a
                    {% if block.settings.button_link_2 == blank %}
                      role="link" aria-disabled="true"
                    {% else %}
                      href="{{ block.settings.button_link_2 }}"
                    {% endif %}
                    class="button{% if block.settings.button_style_secondary_2 %} button--secondary{% else %} button--primary{% endif %}"
                  >
                    {{- block.settings.button_label_2 | escape -}}
                  </a>
                {%- endif -%}
              </div>
          {%- endcase -%}
        {%- endfor -%}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.rich-text.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "left",
          "label": "t:sections.rich-text.settings.desktop_content_position.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.rich-text.settings.desktop_content_position.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.rich-text.settings.desktop_content_position.options__3.label"
        }
      ],
      "default": "center",
      "label": "t:sections.rich-text.settings.desktop_content_position.label"
    },
    {
      "type": "select",
      "id": "content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.rich-text.settings.content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.rich-text.settings.content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.rich-text.settings.content_alignment.options__3.label"
        }
      ],
      "default": "center",
      "label": "t:sections.rich-text.settings.content_alignment.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "default": true,
      "label": "t:sections.rich-text.settings.full_width.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 52
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "t:sections.rich-text.blocks.heading.name",
      "limit": 3,
      "settings": [
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "t:sections.rich-text.blocks.heading.settings.heading.default",
          "label": "t:sections.rich-text.blocks.heading.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "label": "Heading Size",
          "options": [
            {
              "value": "ts-text-clamp_xxl",
              "label": "2X Large"
            },
            {
              "value": "ts-text-clamp_xl",
              "label": "Extra Large"
            },
            {
              "value": "ts-text-clamp_l",
              "label": "Large"
            },
            {
              "value": "ts-text-size_lg",
              "label": "Medium"
            },
            {
              "value": "ts-text-size_md",
              "label": "Small"
            },
            {
              "value": "ts-text-size_sm",
              "label": "Extra Small"
            }
          ],
          "default": "ts-text-clamp_xxl"
        }
      ]
    },
    {
      "type": "caption",
      "name": "t:sections.rich-text.blocks.caption.name",
      "limit": 3,
      "settings": [
        {
          "type": "text",
          "id": "caption",
          "default": "t:sections.rich-text.blocks.caption.settings.text.default",
          "label": "t:sections.rich-text.blocks.caption.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "subtitle",
              "label": "t:sections.rich-text.blocks.caption.settings.text_style.options__1.label"
            },
            {
              "value": "caption-with-letter-spacing",
              "label": "t:sections.rich-text.blocks.caption.settings.text_style.options__2.label"
            }
          ],
          "default": "caption-with-letter-spacing",
          "label": "t:sections.rich-text.blocks.caption.settings.text_style.label"
        },
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "small",
              "label": "t:sections.rich-text.blocks.caption.settings.caption_size.options__1.label"
            },
            {
              "value": "medium",
              "label": "t:sections.rich-text.blocks.caption.settings.caption_size.options__2.label"
            },
            {
              "value": "large",
              "label": "t:sections.rich-text.blocks.caption.settings.caption_size.options__3.label"
            }
          ],
          "default": "medium",
          "label": "t:sections.rich-text.blocks.caption.settings.caption_size.label"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.rich-text.blocks.text.name",
      "limit": 3,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "t:sections.rich-text.blocks.text.settings.text.default",
          "label": "t:sections.rich-text.blocks.text.settings.text.label"
        }
      ]
    },
    {
      "type": "button",
      "name": "t:sections.rich-text.blocks.buttons.name",
      "limit": 2,
      "settings": [
        {
         "type": "header",
         "content": "t:sections.rich-text.blocks.buttons.settings.header_button1.content"
        },        
        {
          "type": "text",
          "id": "button_label",
          "default": "t:sections.rich-text.blocks.buttons.settings.button_label_1.default",
          "label": "t:sections.rich-text.blocks.buttons.settings.button_label_1.label",
          "info": "t:sections.rich-text.blocks.buttons.settings.button_label_1.info"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.rich-text.blocks.buttons.settings.button_link_1.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary",
          "default": false,
          "label": "t:sections.rich-text.blocks.buttons.settings.button_style_secondary_1.label"
        },
        {
         "type": "header",
         "content": "t:sections.rich-text.blocks.buttons.settings.header_button2.content"
        },          
        {
          "type": "text",
          "id": "button_label_2",
          "label": "t:sections.rich-text.blocks.buttons.settings.button_label_2.label",
          "info": "t:sections.rich-text.blocks.buttons.settings.button_label_2.info"
        },
        {
          "type": "url",
          "id": "button_link_2",
          "label": "t:sections.rich-text.blocks.buttons.settings.button_link_2.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary_2",
          "default": false,
          "label": "t:sections.rich-text.blocks.buttons.settings.button_style_secondary_2.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.rich-text.presets.name",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "button"
        }
      ]
    }
  ]
}
{% endschema %}
