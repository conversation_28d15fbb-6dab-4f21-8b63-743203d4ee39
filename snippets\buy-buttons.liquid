{% comment %}
  Renders product buy-buttons.
  Accepts:
  - product: {Object} product object.
  - block: {Object} passing the block information.
  - product_form_id: {String} product form id.
  - section_id: {String} id of section to which this snippet belongs.
  - show_pickup_availability: {<PERSON><PERSON><PERSON>} for the pickup availability. If true the pickup availability is rendered, false - not rendered (optional).

  Usage:
  {% render 'buy-buttons', block: block, product: product, product_form_id: product_form_id, section_id: section.id, show_pickup_availability: true %}
{% endcomment %}
<div {{ block.shopify_attributes }}>
  {%- if product != blank -%}
    {%- liquid
      assign gift_card_recipient_feature_active = false
      if block.settings.show_gift_card_recipient and product.gift_card?
        assign gift_card_recipient_feature_active = true
      endif

      assign show_dynamic_checkout = false
      if block.settings.show_dynamic_checkout and gift_card_recipient_feature_active == false
        assign show_dynamic_checkout = true
      endif
    -%}
    {%- unless product.tags contains 'coming_soon' -%}
      <product-form
        class="product-form !ts-mb-4"
        data-hide-errors="{{ gift_card_recipient_feature_active }}"
        data-section-id="{{ section.id }}"
      >
        <div class="product-form__error-message-wrapper" role="alert" hidden>
          <span class="svg-wrapper">
            {{- 'icon-error.svg' | inline_asset_content -}}
          </span>
          <span class="product-form__error-message"></span>
        </div>
  
        {%- form 'product',
          product,
          id: product_form_id,
          class: 'form',
          novalidate: 'novalidate',
          data-type: 'add-to-cart-form'
        -%}
          <input
            type="hidden"
            name="id"
            value="{{ product.selected_or_first_available_variant.id }}"
            {% if product.selected_or_first_available_variant.available == false
              or quantity_rule_soldout
              or product.selected_or_first_available_variant == null
            %}
              disabled
            {% endif %}
            class="product-variant-id"
          >
  
          {%- if gift_card_recipient_feature_active -%}
            {%- render 'gift-card-recipient-form', product: product, form: form, section: section -%}
          {%- endif -%}
  
          <div class="product-form__buttons !ts-max-w-full">
            {%- liquid
              assign check_against_inventory = true
              if product.selected_or_first_available_variant.inventory_management != 'shopify' or product.selected_or_first_available_variant.inventory_policy == 'continue'
                assign check_against_inventory = false
              endif
              if product.selected_or_first_available_variant.quantity_rule.min > product.selected_or_first_available_variant.inventory_quantity and check_against_inventory
                assign quantity_rule_soldout = true
              endif
            -%}
            <button
              id="ProductSubmitButton-{{ section_id }}"
              type="submit"
              name="add"
              class="product-form__submit button button--full-width {% if show_dynamic_checkout %}button--secondary{% else %}button--primary{% endif %} !ts-mb-0 ts-text-size_sm ts-font-medium hover:!ts-text-[rgba(var(--color-background))]"
              {% if product.selected_or_first_available_variant.available == false
                or quantity_rule_soldout
                or product.selected_or_first_available_variant == null
              %}
                disabled
              {% endif %}
            >
              <span>
                {%- if product.selected_or_first_available_variant == null -%}
                  {{ 'products.product.unavailable' | t }}
                {%- elsif product.selected_or_first_available_variant.available == false or quantity_rule_soldout -%}
                  {{ 'products.product.sold_out' | t }}
                {%- else -%}
                  {{ 'products.product.add_to_cart' | t }}
                {%- endif -%}
              </span>
              {%- render 'loading-spinner' -%}
            </button>
            {%- if show_dynamic_checkout -%}
              {{ form | payment_button }}
            {%- endif -%}
          </div>
        {%- endform -%}
      </product-form>
    {%- else -%}
      <div class="product-form__buttons form !ts-max-w-full">
        <button
          type="submit"
          name="add"
          class="product-form__submit button button--full-width button--primary !ts-mb-0 ts-text-size_sm ts-font-medium hover:!ts-text-[rgba(var(--color-background))] ts-uppercase !ts-opacity-100"
          disabled
        >
          {{ 'products.product.coming_soon' | t }}
        </button>
      </div>
    {%- endunless -%}
  {%- else -%}
    <div class="product-form">
      <div class="product-form__buttons form">
        <button
          type="submit"
          name="add"
          class="product-form__submit button button--full-width button--primary !ts-mb-0 ts-text-size_sm ts-font-medium hover:!ts-text-[rgba(var(--color-background))]"
          disabled
        >
          {{ 'products.product.sold_out' | t }}
        </button>
      </div>
    </div>
  {%- endif -%}

  {%- if show_pickup_availability -%}
    {{ 'component-pickup-availability.css' | asset_url | stylesheet_tag }}

    {%- assign pick_up_availabilities = product.selected_or_first_available_variant.store_availabilities
      | where: 'pick_up_enabled', true
    -%}

    <pickup-availability
      class="product__pickup-availabilities quick-add-hidden !ts-min-h-0"
      {% if product.selected_or_first_available_variant.available and pick_up_availabilities.size > 0 %}
        available
      {% endif %}
      data-root-url="{{ routes.root_url }}"
      data-variant-id="{{ product.selected_or_first_available_variant.id }}"
      data-has-only-default-variant="{{ product.has_only_default_variant }}"
      data-product-page-color-scheme="gradient color-{{ section.settings.color_scheme }}"
    >
      <template>
        <pickup-availability-preview class="pickup-availability-preview !ts-p-0">
          <span class="svg-wrapper">
            {{- 'icon-unavailable.svg' | inline_asset_content -}}
          </span>
          <div class="pickup-availability-info">
            <p class="caption-large">{{ 'products.product.pickup_availability.unavailable' | t }}</p>
            <button class="pickup-availability-button link link--text underlined-link">
              {{ 'products.product.pickup_availability.refresh' | t }}
            </button>
          </div>
        </pickup-availability-preview>
      </template>
    </pickup-availability>

    <script src="{{ 'pickup-availability.js' | asset_url }}" defer="defer"></script>
  {%- endif -%}
</div>

{% render 'mw_badges', product: product %}