{{ 'component-image-with-text.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  assign fetch_priority = 'auto'
  if section.index == 1
    assign fetch_priority = 'high'
  endif
  if section.settings.color_scheme == section.settings.section_color_scheme and section.settings.content_layout == 'no-overlap'
    assign remove_color_classes = true
  endif
-%}

<div class="section-{{ section.id }}-padding gradient color-{{ section.settings.section_color_scheme }}">
  <div class="page-width{% if section.settings.fullwidth_section %} !ts-px-0{% endif %}">
    <div class="image-with-text image-with-text--{{ section.settings.content_layout }} isolate{% if settings.text_boxes_border_thickness > 0 and settings.text_boxes_border_opacity > 0 and settings.media_border_thickness > 0 and settings.media_border_opacity > 0 %} collapse-borders{% endif %}{% unless section.settings.color_scheme == section.settings.section_color_scheme and settings.media_border_thickness > 0 and settings.text_boxes_shadow_opacity == 0 and settings.text_boxes_border_thickness == 0 or settings.text_boxes_border_opacity == 0 %} collapse-corners{% endunless %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
      <div class="image-with-text__grid grid grid--gapless grid--1-col grid--{% if section.settings.desktop_image_width == 'medium' %}2-col-tablet{% else %}3-col-tablet{% endif %}{% if section.settings.layout == 'text_first' %} image-with-text__grid--reverse{% endif %}">
        <div class="image-with-text__media-item image-with-text__media-item--{{ section.settings.desktop_image_width }} image-with-text__media-item--{{ section.settings.desktop_content_position }} grid__item">
          {%- if section.settings.link != blank and section.settings.link_text != blank -%}
            <a href="{{ section.settings.link }}" class="ts-absolute ts-top-0 ts-left-0 ts-text-[0] ts-w-full ts-h-full ts-z-[1]" aria-label="{{ section.settings.link_text }}" rel="nofollow" target="_blank">
              {{ section.settings.link_text }}
            </a>
          {%- endif -%}
          <div
            class="image-with-text__media image-with-text__media--{{ section.settings.height }} global-media-settings{% unless remove_color_classes %} gradient color-{{ section.settings.color_scheme }}{% else %} background-transparent{% endunless %}{% if section.settings.image != blank %} media{% else %} image-with-text__media--placeholder placeholder{% endif %}{% if section.settings.image_behavior != 'none' %} animate--{{ section.settings.image_behavior }}{% endif %}"
            {% if section.settings.height == 'adapt' and section.settings.image != blank %}
              style="padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%;"
            {% endif %}
          >
            {%- if section.settings.image != blank -%}
              {%- if section.settings.image_behavior == 'ambient' or section.settings.image_behavior == 'zoom-in' -%}
                {%- assign widths = '198, 432, 642, 900, 1284, 1800' -%}
                {%- capture sizes -%}
              (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 1.6667 }}px,
              (min-width: 750px) calc((100vw - 130px) / 1.667), calc((100vw - 50px) / 1.667)
            {%- endcapture -%}
              {%- else -%}
                {%- assign widths = '165, 360, 535, 750, 1070, 1500' -%}
                {%- capture sizes -%}
              (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px,
              (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)
            {%- endcapture -%}
              {%- endif -%}
              {{
                section.settings.image
                | image_url: width: 1500
                | image_tag: sizes: sizes, widths: widths, fetchpriority: fetch_priority
              }}
            {%- else -%}
              {{ 'detailed-apparel-1' | placeholder_svg_tag: 'placeholder-svg' }}
            {%- endif -%}
          </div>
        </div>
        <div class="image-with-text__text-item grid__item">
          <div
            id="ImageWithText--{{ section.id }}"
            class="image-with-text__content image-with-text__content--{{ section.settings.desktop_content_position }} image-with-text__content--desktop-{{ section.settings.desktop_content_alignment }} image-with-text__content--mobile-{{ section.settings.mobile_content_alignment }} image-with-text__content--{{ section.settings.height }} content-container{% unless remove_color_classes %} gradient color-{{ section.settings.color_scheme }}{% else %} background-transparent{% endunless %}"
          >
            {%- for block in section.blocks -%}
              {% case block.type %}
                {%- when 'heading' -%}
                  <h2
                    class="image-with-text__heading inline-richtext {{ block.settings.heading_size }} ts-font-black"
                    {{ block.shopify_attributes }}
                  >
                    {{ block.settings.heading }}
                  </h2>
                {%- when 'caption' -%}
                  <p
                    class="image-with-text__text image-with-text__text--caption {{ block.settings.text_style }} {{ block.settings.text_style }}--{{ block.settings.text_size }} {{ block.settings.text_style }}"
                    {{ block.shopify_attributes }}
                  >
                    {{ block.settings.caption | escape }}
                  </p>
                {%- when 'text' -%}
                  <div class="image-with-text__text rte {{ block.settings.text_style }} ts-text-size_md" {{ block.shopify_attributes }}>
                    {{ block.settings.text }}
                  </div>
                {%- when 'button' -%}
                  {%- if block.settings.button_label != blank -%}
                    <a
                      {% if block.settings.button_link == blank %}
                        role="link" aria-disabled="true"
                      {% else %}
                        href="{{ block.settings.button_link }}"
                      {% endif %}
                      class="button{% if block.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}"
                      {{ block.shopify_attributes }}
                    >
                      {{ block.settings.button_label | escape }}
                    </a>
                  {%- endif -%}
              {%- endcase -%}
            {%- endfor -%}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.image-with-text.name",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "fullwidth_section",
      "default": false,
      "label": "Fullwidth Section"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.image-with-text.settings.image.label"
    },
    {
      "type": "select",
      "id": "height",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.image-with-text.settings.height.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.image-with-text.settings.height.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.image-with-text.settings.height.options__3.label"
        },
        {
          "value": "large",
          "label": "t:sections.image-with-text.settings.height.options__4.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.image-with-text.settings.height.label"
    },
    {
      "type": "select",
      "id": "desktop_image_width",
      "options": [
        {
          "value": "small",
          "label": "t:sections.image-with-text.settings.desktop_image_width.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.image-with-text.settings.desktop_image_width.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.image-with-text.settings.desktop_image_width.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.image-with-text.settings.desktop_image_width.label"
    },
    {
      "type": "select",
      "id": "layout",
      "options": [
        {
          "value": "image_first",
          "label": "t:sections.image-with-text.settings.layout.options__1.label"
        },
        {
          "value": "text_first",
          "label": "t:sections.image-with-text.settings.layout.options__2.label"
        }
      ],
      "default": "image_first",
      "label": "t:sections.image-with-text.settings.layout.label"
    },
    {
      "type": "select",
      "id": "image_behavior",
      "options": [
        {
          "value": "none",
          "label": "t:sections.all.animation.image_behavior.options__1.label"
        },
        {
          "value": "ambient",
          "label": "t:sections.all.animation.image_behavior.options__2.label"
        },
        {
          "value": "zoom-in",
          "label": "t:sections.all.animation.image_behavior.options__4.label"
        }
      ],
      "default": "none",
      "label": "t:sections.all.animation.image_behavior.label"
    },
    {
      "type": "url",
      "id": "link",
      "label": "Redirect URL"
    },
    {
      "type": "text",
      "id": "link_text",
      "label": "Link Text"
    },
    {
      "type": "header",
      "content": "t:sections.image-with-text.settings.header.content"
    }, 
    {
      "type": "select",
      "id": "content_layout",
      "options": [
        {
          "value": "no-overlap",
          "label": "t:sections.image-with-text.settings.content_layout.options__1.label"
        },
        {
          "value": "overlap",
          "label": "t:sections.image-with-text.settings.content_layout.options__2.label"
        }
      ],
      "default": "no-overlap",
      "label": "t:sections.image-with-text.settings.content_layout.label"
    },       
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "top",
          "label": "t:sections.image-with-text.settings.desktop_content_position.options__1.label"
        },
        {
          "value": "middle",
          "label": "t:sections.image-with-text.settings.desktop_content_position.options__2.label"
        },
        {
          "value": "bottom",
          "label": "t:sections.image-with-text.settings.desktop_content_position.options__3.label"
        }
      ],
      "default": "top",
      "label": "t:sections.image-with-text.settings.desktop_content_position.label"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__3.label"
        }
      ],
      "default": "left",
      "label": "t:sections.image-with-text.settings.desktop_content_alignment.label"
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.image-with-text.settings.mobile_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.image-with-text.settings.mobile_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.image-with-text.settings.mobile_content_alignment.options__3.label"
        }
      ],
      "default": "left",
      "label": "t:sections.image-with-text.settings.mobile_content_alignment.label"
    },
    {
      "type": "header",
      "content": "t:sections.image-with-text.settings.header_colors.content"
    },        
    {
      "type": "color_scheme",
      "id": "section_color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.multirow.settings.container_color_scheme.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "t:sections.image-with-text.blocks.heading.name",
      "limit": 1,
      "settings": [
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "t:sections.image-with-text.blocks.heading.settings.heading.default",
          "label": "t:sections.image-with-text.blocks.heading.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "label": "Heading Size",
          "options": [
            {
              "value": "ts-text-clamp_xxl",
              "label": "2X Large"
            },
            {
              "value": "ts-text-clamp_xl",
              "label": "Extra Large"
            },
            {
              "value": "ts-text-clamp_l",
              "label": "Large"
            },
            {
              "value": "ts-text-size_lg",
              "label": "Medium"
            },
            {
              "value": "ts-text-size_md",
              "label": "Small"
            },
            {
              "value": "ts-text-size_sm",
              "label": "Extra Small"
            }
          ],
          "default": "ts-text-clamp_xxl"
        }
      ]
    },
    {
      "type": "caption",
      "name": "t:sections.image-with-text.blocks.caption.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "caption",
          "default": "t:sections.image-with-text.blocks.caption.settings.text.default",
          "label": "t:sections.image-with-text.blocks.caption.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "subtitle",
              "label": "t:sections.image-with-text.blocks.caption.settings.text_style.options__1.label"
            },
            {
              "value": "caption-with-letter-spacing",
              "label": "t:sections.image-with-text.blocks.caption.settings.text_style.options__2.label"
            }
          ],
          "default": "caption-with-letter-spacing",
          "label": "t:sections.image-with-text.blocks.caption.settings.text_style.label"
        },
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "small",
              "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.options__1.label"
            },
            {
              "value": "medium",
              "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.options__2.label"
            },
            {
              "value": "large",
              "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.options__3.label"
            }
          ],
          "default": "medium",
          "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.label"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.image-with-text.blocks.text.name",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "t:sections.image-with-text.blocks.text.settings.text.default",
          "label": "t:sections.image-with-text.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.image-with-text.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.image-with-text.blocks.text.settings.text_style.options__2.label"
            }
          ],
          "default": "body",
          "label": "t:sections.image-with-text.blocks.text.settings.text_style.label"
        }
      ]
    },
    {
      "type": "button",
      "name": "t:sections.image-with-text.blocks.button.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "default": "t:sections.image-with-text.blocks.button.settings.button_label.default",
          "label": "t:sections.image-with-text.blocks.button.settings.button_label.label",
          "info": "t:sections.image-with-text.blocks.button.settings.button_label.info"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.image-with-text.blocks.button.settings.button_link.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary",
          "default": false,
          "label": "t:sections.image-with-text.blocks.button.settings.outline_button.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.image-with-text.presets.name",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "button"
        }
      ]
    }
  ]
}
{% endschema %}
