{{ 'section-contact-form.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="color-{{ section.settings.color_scheme }} gradient">
  <div class="contact page-width page-width--narrow section-{{ section.id }}-padding !ts-max-w-[55.6rem]">
    <div class="contact-text-contents ts-mb-6">
      {%- if section.settings.heading != blank -%}
        <h2 class="title title-wrapper--no-top-margin inline-richtext {{ section.settings.heading_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %} ts-font-black md:ts-text-center ts-mb-6">
          {{ section.settings.heading }}
        </h2>
      {%- else -%}
        <h2 class="visually-hidden">{{ 'templates.contact.form.title' | t }}</h2>
      {%- endif -%}
  
      {%- if section.settings.body_text != blank -%}
        <div class="body-text rte ts-text-size_md{% if section.blocks.size > 0 %} md:ts-mb-[1.8rem] ts-mb-6{% endif %}">
          {{ section.settings.body_text }}
        </div>
      {%- endif -%}

      {%- for block in section.blocks -%}
        {%- assign item = block.settings -%}

        {%- case block.type -%}
          {%- when 'contact-details' -%}
            <div class="contact-detail ts-text-size_md ts-flex ts-gap-6 ts-mt-4" {{ block.shopify_attributes }}>
              {%- if item.label != blank -%}
                <div class="contact-detail-label ts-basis-40">
                  <b>{{ item.label }}</b>
                </div>
              {%- endif -%}

              <div class="contact-detail-info ts-flex-1">
                {% for i in (1..3) %}
                  {% assign info_id = 'info_' | append: i %}
                  {% assign info = item[info_id] %}
                  
                  {%- if info != blank -%}
                    <span class="ts-block">{{ info }}</span>
                  {%- endif -%}
                {% endfor %}
              </div>
            </div>
          {%- else -%}
            {%- if item.richtext != blank -%}
              <div class="contact-richtext ts-text-size_md rte md:ts-mt-[1.8rem] ts-mt-6" {{ block.shopify_attributes }}>
                {{ item.richtext }}
              </div>
            {%- endif -%}
        {%- endcase -%}
      {%- endfor -%}
    </div>

    {%- liquid
      assign contact_form_class = 'isolate'
      if settings.animations_reveal_on_scroll
        assign contact_form_class = 'isolate scroll-trigger animate--slide-in'
      endif
    -%}
    {%- form 'contact', id: 'ContactForm', class: contact_form_class -%}
      {%- if form.posted_successfully? -%}
        <h2 class="form-status form-status-list form__message" tabindex="-1" autofocus>
          {{- 'icon-success.svg' | inline_asset_content -}}
          {{ 'templates.contact.form.post_success' | t }}
        </h2>
      {%- elsif form.errors -%}
        <div class="form__message">
          <h2 class="form-status caption-large text-body" role="alert" tabindex="-1" autofocus>
            {{- 'icon-error.svg' | inline_asset_content -}}
            {{ 'templates.contact.form.error_heading' | t }}
          </h2>
        </div>
        <ul class="form-status-list caption-large" role="list">
          <li>
            <a href="#ContactForm-email" class="link">
              {{ form.errors.translated_fields.email | capitalize }}
              {{ form.errors.messages.email }}
            </a>
          </li>
        </ul>
      {%- endif -%}
      <div class="field">
        <input
          class="field__input"
          autocomplete="name"
          type="text"
          id="ContactForm-name"
          name="contact[{{ 'templates.contact.form.name' | t }}]"
          value="{% if form.name %}{{ form.name }}{% elsif customer %}{{ customer.name }}{% endif %}"
          placeholder="{{ 'templates.contact.form.name' | t }}"
        >
        <label class="field__label" for="ContactForm-name">{{ 'templates.contact.form.name' | t }}</label>
      </div>
      <div class="field field--with-error">
        <input
          autocomplete="email"
          type="email"
          id="ContactForm-email"
          class="field__input"
          name="contact[email]"
          spellcheck="false"
          autocapitalize="off"
          value="{% if form.email %}{{ form.email }}{% elsif customer %}{{ customer.email }}{% endif %}"
          aria-required="true"
          {% if form.errors contains 'email' %}
            aria-invalid="true"
            aria-describedby="ContactForm-email-error"
          {% endif %}
          placeholder="{{ 'templates.contact.form.email' | t }}"
        >
        <label class="field__label" for="ContactForm-email">
          {{- 'templates.contact.form.email' | t }}
          <span aria-hidden="true">*</span></label
        >
        {%- if form.errors contains 'email' -%}
          <small class="contact__field-error" id="ContactForm-email-error">
            <span class="visually-hidden">{{ 'accessibility.error' | t }}</span>
            <span class="form__message">
              <span class="svg-wrapper">
                {{- 'icon-error.svg' | inline_asset_content -}}
              </span>
              {{- form.errors.translated_fields.email | capitalize }}
              {{ form.errors.messages.email -}}
            </span>
          </small>
        {%- endif -%}
      </div>
      <div class="field">
        <input
          type="tel"
          id="ContactForm-phone"
          class="field__input"
          autocomplete="tel"
          name="contact[{{ 'templates.contact.form.phone' | t }}]"
          pattern="[0-9\-]*"
          value="{% if form.phone %}{{ form.phone }}{% elsif customer %}{{ customer.phone }}{% endif %}"
          placeholder="{{ 'templates.contact.form.phone' | t }}"
        >
        <label class="field__label" for="ContactForm-phone">{{ 'templates.contact.form.phone' | t }}</label>
      </div>
      <div class="field">
        <textarea
          rows="10"
          id="ContactForm-body"
          class="text-area field__input"
          name="contact[{{ 'templates.contact.form.comment' | t }}]"
          placeholder="{{ 'templates.contact.form.comment' | t }}"
        >
          {{- form.body -}}
        </textarea>
        <label class="form__label field__label" for="ContactForm-body">
          {{- 'templates.contact.form.comment' | t -}}
        </label>
      </div>

      {%- if section.settings.disclaimer != blank -%}
        <div class="disclaimer-text ts-text-size_sm ts-opacity-70">
          {{ section.settings.disclaimer }}
        </div>
      {%- endif -%}

      <div class="contact__button">
        <button type="submit" class="button button--full-width">
          {{ 'templates.contact.form.send' | t }}
        </button>
      </div>
    {%- endform -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.contact-form.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "heading",
      "default": "t:sections.contact-form.settings.title.default",
      "label": "t:sections.contact-form.settings.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "Heading Size",
      "options": [
        {
          "value": "ts-text-clamp_xxl",
          "label": "2X Large"
        },
        {
          "value": "ts-text-clamp_xl",
          "label": "Extra Large"
        },
        {
          "value": "ts-text-clamp_l",
          "label": "Large"
        },
        {
          "value": "ts-text-size_lg",
          "label": "Medium"
        },
        {
          "value": "ts-text-size_md",
          "label": "Small"
        },
        {
          "value": "ts-text-size_sm",
          "label": "Extra Small"
        }
      ],
      "default": "ts-text-clamp_l"
    },
    {
      "type": "richtext",
      "id": "body_text",
      "label": "Body Text"
    },
    {
      "type": "richtext",
      "id": "disclaimer",
      "label": "Disclaimer copy"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "contact-details",
      "name": "Contact Details",
      "settings": [
        {
          "type": "text",
          "id": "label",
          "label": "Contact Label",
          "default": "Details"
        },
        {
          "type": "inline_richtext",
          "id": "info_1",
          "label": "Contact Info"
        },
        {
          "type": "inline_richtext",
          "id": "info_2",
          "label": "Contact Info"
        },
        {
          "type": "inline_richtext",
          "id": "info_3",
          "label": "Contact Info"
        }
      ]
    },
    {
      "type": "richtext",
      "name": "Richtext",
      "settings": [
        {
          "type": "richtext",
          "id": "richtext",
          "label": "Richtext",
          "default": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.contact-form.presets.name"
    }
  ]
}
{% endschema %}
