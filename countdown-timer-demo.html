<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Countdown Timer Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .demo-section h2 {
            margin-top: 0;
            color: #555;
        }
        .test-controls {
            margin: 20px 0;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Countdown Timer Demo</h1>
        
        <div class="info">
            <strong>Note:</strong> This is a demo of the countdown timer web component. 
            In your Shopify store, this will be automatically integrated into product pages 
            when you add the "coming_soon" tag and set the countdown_date metafield.
        </div>

        <div class="demo-section">
            <h2>Demo 1: Countdown to Future Date</h2>
            <p>This countdown timer is set to expire in 2 minutes from now:</p>
            <countdown-timer id="demo1"></countdown-timer>
        </div>

        <div class="demo-section">
            <h2>Demo 2: Countdown to Tomorrow</h2>
            <p>This countdown timer is set to expire tomorrow at noon:</p>
            <countdown-timer id="demo2"></countdown-timer>
        </div>

        <div class="demo-section">
            <h2>Demo 3: Error State</h2>
            <p>This shows what happens with an invalid date:</p>
            <countdown-timer data-target-date="invalid-date"></countdown-timer>
        </div>

        <div class="test-controls">
            <h3>Test Controls</h3>
            <button onclick="setShortCountdown()">Set 10 Second Countdown</button>
            <button onclick="resetDemo()">Reset Demos</button>
            <p><small>Use these buttons to test the countdown functionality</small></p>
        </div>
    </div>

    <!-- Include the countdown timer CSS and JS -->
    <link rel="stylesheet" href="assets/component-countdown-timer.css">
    <script src="assets/countdown-timer.js"></script>

    <script>
        // Set up demo countdowns
        function setupDemos() {
            // Demo 1: 2 minutes from now
            const demo1Date = new Date();
            demo1Date.setMinutes(demo1Date.getMinutes() + 2);
            document.getElementById('demo1').setAttribute('data-target-date', demo1Date.toISOString());

            // Demo 2: Tomorrow at noon
            const demo2Date = new Date();
            demo2Date.setDate(demo2Date.getDate() + 1);
            demo2Date.setHours(12, 0, 0, 0);
            document.getElementById('demo2').setAttribute('data-target-date', demo2Date.toISOString());
        }

        function setShortCountdown() {
            const shortDate = new Date();
            shortDate.setSeconds(shortDate.getSeconds() + 10);
            document.getElementById('demo1').setAttribute('data-target-date', shortDate.toISOString());
        }

        function resetDemo() {
            setupDemos();
        }

        // Initialize demos when page loads
        document.addEventListener('DOMContentLoaded', setupDemos);
    </script>
</body>
</html>
