{%- liquid
	assign widths = '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'
	assign image_class = 'motion-reduce ts-w-full ts-max-w-full ts-h-auto' | append: class
	assign fetch_priority = 'auto'
	assign mobile_image = image | image_url: width: image.width
	assign aspect_ratio = image.aspect_ratio

	if section.index == 1
		assign fetch_priority = 'high'
	endif

	if small_image != blank
		assign mobile_image = small_image | image_url: width: small_image.width
	endif

	if ratio != blank
		assign aspect_ratio = ratio
	endif
-%}

<div class="img-wrapper ts-w-full ts-h-full ts-overflow-hidden" style="aspect-ratio: {{ aspect_ratio }};">
	<picture class="media ts-w-full ts-h-full">
			<source srcset="{{ mobile_image }}" media="(max-width: 767px)"/>
			{{
				image
				| image_url: width: 3840
				| image_tag: height: image.height, sizes: sizes, widths: widths, fetchpriority: fetch_priority, class: image_class, alt: image.alt
			}}
		</picture>
</div>