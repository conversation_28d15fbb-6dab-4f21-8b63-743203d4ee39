{% comment %}
  Renders a megamenu for the header.

  Usage:
  {% render 'header-mega-menu' %}
{% endcomment %}

<nav class="header__inline-menu xl:!ts-block !ts-hidden">
  <ul class="list-menu list-menu--inline" role="list">
    {%- for link in section.settings.menu.links -%}
      <li>
        {%- if link.links != blank -%}
          <header-menu>
            <details id="Details-HeaderMenu-{{ forloop.index }}" class="mega-menu">
              <summary
                id="HeaderMenu-{{ link.handle }}"
                class="header__menu-item list-menu__item link focus-inset"
              >
                <span
                  {%- if link.child_active %}
                    class="header__active-menu-item"
                  {% endif %}
                >
                  {{- link.title | escape -}}
                </span>
                {{- 'icon-caret.svg' | inline_asset_content -}}
              </summary>
              <div
                id="MegaMenu-Content-{{ forloop.index }}"
                class="mega-menu__content color-{{ section.settings.menu_color_scheme }} gradient motion-reduce global-settings-popup ts-px-[1.6rem] ts-flex ts-gap-x-10 ts-items-start"
                tabindex="-1"
              >
                <ul
                  class="mega-menu__list{% if link.levels == 1 %} mega-menu__list--condensed{% endif %} ts-flex-1"
                  role="list"
                >
                  {%- for childlink in link.links -%}
                    <li>
                      <a
                        id="HeaderMenu-{{ link.handle }}-{{ childlink.handle }}"
                        href="{{ childlink.url }}"
                        class="mega-menu__link mega-menu__link--level-2 link{% if childlink.current %} mega-menu__link--active{% endif %} ts-mb-8"
                        {% if childlink.current %}
                          aria-current="page"
                        {% endif %}
                      >
                        {{ childlink.title | escape }}
                      </a>
                      {%- if childlink.links != blank -%}
                        <ul class="list-unstyled" role="list">
                          {%- for grandchildlink in childlink.links -%}
                            <li>
                              <a
                                id="HeaderMenu-{{ link.handle }}-{{ childlink.handle }}-{{ grandchildlink.handle }}"
                                href="{{ grandchildlink.url }}"
                                class="mega-menu__link link{% if grandchildlink.current %} mega-menu__link--active{% endif %}"
                                {% if grandchildlink.current %}
                                  aria-current="page"
                                {% endif %}
                              >
                                {{ grandchildlink.title | escape }}
                              </a>
                            </li>
                          {%- endfor -%}
                        </ul>
                      {%- endif -%}
                    </li>
                  {%- endfor -%}
                </ul>

                {%- for block in section.blocks -%}
                  {%- if block.type == 'featured_product' -%}
                    {% liquid
                      assign product = block.settings.product
                      assign image = product.featured_image

                      if block.settings.image != blank
                        assign image = block.settings.image
                      endif
                    %}
                    
                    {%- if product.available -%}
                      <div class="mega-menu-new-product ts-basis-[clamp(28rem,21vw,40rem)] ts-pt-16">
                        <a href="{{ product.url }}" class="new-product-link ts-relative" aria-label="{{ product.title }}">
                          <span class="badge-wrapper ts-absolute -ts-top-6 -ts-left-16 ts-z-[1]">
                            <img src="/cdn/shop/files/new_badge.png?v=1746186014&width=80" alt="New Product Badge" class="ts-max-w-32" width="80" height="80">
                          </span>
                          {% render 'mw_image', image: image, class: 'mega-menu--featured-product-img', ratio: 1 %}
                        </a>
                      </div>
                    {%- endif -%}
                  {%- endif -%}
                {%- endfor -%}
              </div>
            </details>
          </header-menu>
        {%- else -%}
          <a
            id="HeaderMenu-{{ link.handle }}"
            href="{{ link.url }}"
            class="header__menu-item list-menu__item link link--text focus-inset"
            {% if link.current %}
              aria-current="page"
            {% endif %}
          >
            <span
              {%- if link.current %}
                class="header__active-menu-item"
              {% endif %}
            >
              {{- link.title | escape -}}
            </span>
          </a>
        {%- endif -%}
      </li>
    {%- endfor -%}
  </ul>
</nav>