.featured-product .product__media-list {
  width: 100%;
  margin: 0;
  padding-bottom: 0;
}

.featured-product .product-media-container {
  margin-bottom: var(--media-shadow-vertical-offset);
  max-width: 100%;
}

.featured-product .product__media-item {
  padding-left: 0;
}

.featured-product .placeholder-svg {
  display: block;
  height: auto;
  width: 100%;
}

.background-secondary .featured-product {
  padding: 2.5rem;
}

.featured-product .share-button:nth-last-child(2) {
  display: inline-flex;
}

.share-button + .product__view-details {
  display: inline-flex;
  float: right;
  align-items: center;
  min-height: 4.4rem;
}

.share-button + .product__view-details::after {
  content: '';
  clear: both;
  display: table;
}

@media screen and (min-width: 750px) {
  .featured-product .product__media-item {
    padding-bottom: 0;
  }

  .background-secondary .featured-product {
    padding: 5rem;
  }

  .product--right .product__media-wrapper {
    order: 2;
  }
}

@media screen and (min-width: 990px) {
  .background-secondary .featured-product:not(.product--no-media) > .product__info-wrapper {
    padding: 0 0 0 5rem;
  }

  .background-secondary .featured-product:not(.product--no-media).product--right > .product__info-wrapper {
    padding: 0 5rem 0 0;
  }

  .featured-product:not(.product--no-media) > .product__info-wrapper {
    padding: 0 7rem;
  }

  .background-secondary .featured-product {
    padding: 6rem 7rem;
    position: relative;
    z-index: 1;
  }
}
