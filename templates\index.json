{"sections": {"slideshow_r9aHjb": {"type": "slideshow", "blocks": {"slide_9NQM8L": {"type": "slide", "settings": {"image": "shopify://shop_images/A_ONE.png", "image_overlay_opacity": 0, "heading": "", "heading_size": "h1", "subheading": "", "button_label": "", "link": "shopify://products/aone-unapologetic-gs-fierce-purple-metallic-gold-hyper-violet", "button_style_secondary": false, "show_text_box": false, "box_align": "middle-center", "text_alignment": "center", "text_alignment_mobile": "center", "color_scheme": ""}}, "slide_YEATeD": {"type": "slide", "settings": {"image": "shopify://shop_images/HIGHER_RES.png", "image_overlay_opacity": 0, "heading": "", "heading_size": "h1", "subheading": "", "button_label": "", "link": "shopify://collections/toronto-tempo", "button_style_secondary": false, "show_text_box": false, "box_align": "middle-center", "text_alignment": "center", "text_alignment_mobile": "center", "color_scheme": ""}}}, "block_order": ["slide_9NQM8L", "slide_YEATeD"], "custom_css": [], "settings": {"layout": "full_bleed", "slide_height": "adapt_image", "slider_visual": "dots", "auto_rotate": true, "change_slides_speed": 5, "image_behavior": "none", "show_text_below": false, "accessibility_info": ""}}, "mw_featured_collection_9T79W4": {"type": "mw_featured_collection", "settings": {"color_scheme": "", "title": "Toronto Tempo", "title_size": "ts-text-size_lg", "collection": "toronto-tempo", "view_all": true, "prodoct_limit": 10, "view_link_label": "Shop New Arrivals", "text_alignment": "center", "view_all_style": "link", "container_width": 1920, "full_width": false, "box_color_scheme": "", "bordered": false, "image_ratio": "square", "image_shape": "default", "show_secondary_image": true, "show_vendor": false, "show_rating": false, "quick_add": "standard", "d_slides": 4, "l_slides": 3, "t_slides": 2, "m_slides": 2, "d_space": 80, "l_space": 70, "t_space": 60, "m_space": 10, "enable_loop": true, "center_mode": false, "mousewheel": true, "enable_autoplay": true, "autoplay_speed": 5000, "enable_navigation": true, "enable_pagination": false, "destroy_swiper": true, "pad_desk": 40, "pad_lap": 40, "pad_tab": 40, "pad_mob": 30}}, "multicolumn_7V79mm": {"type": "multicolumn", "blocks": {"column_RE8A9n": {"type": "column", "settings": {"media_type": "image", "image": "shopify://shop_images/Jpeg_72-SS25_CLARKS_ORIGINALS_WOMENS_TIER1_WALLABEE_WEAVE_POWDER_BLUE_SUEDE2_26180614_1080x1350_STORY.jpg", "media_url": "", "title": "<PERSON>", "text": "", "link_label": "Shop Now", "link": "shopify://collections/clarks"}}, "column_pFqpLk": {"type": "column", "settings": {"media_type": "image", "image": "shopify://shop_images/SS25-Clarks-Originals-Womens-<PERSON><PERSON><PERSON>-<PERSON>_White-Gingham6-2618067-1080x1350-FEED.jpg", "media_url": "", "video": "shopify://files/videos/MAKEWAY X NIKE P-6000.mp4", "video_poster": "shopify://shop_images/MAKEWAY-X-NIKE-P-6000-video_poster.jpg", "title": "", "text": "<p> </p>", "link_label": "", "link": ""}}}, "block_order": ["column_RE8A9n", "column_pFqpLk"], "settings": {"title": "", "heading_size": "h1", "image_width": "half", "image_ratio": "portrait", "button_label": "", "button_link": "", "full_width": true, "overlay_content": true, "columns_desktop": 2, "column_alignment": "left", "background_style": "none", "color_scheme": "scheme-3", "columns_mobile": "1", "swipe_on_mobile": false, "padding_top": 0, "padding_bottom": 0}}, "mw_banner_NANQWY": {"type": "mw_banner", "blocks": {"heading_txWXYi": {"type": "heading", "settings": {"type": "title", "heading_text": "", "heading_size": "ts-text-clamp_l", "text_transform": "ts-uppercase"}}, "buttons_G8h9JP": {"type": "buttons", "settings": {"button_url": "shopify://products/xt-6-gtx-oxford-tan-almond-milk-safari", "button_text": ""}}}, "block_order": ["heading_txWXYi", "buttons_G8h9JP"], "disabled": true, "settings": {"color_scheme": "scheme-3", "banner_type": "image", "add_overlay": true, "overlay_opacity": 0.1, "banner_image": "shopify://shop_images/Option_2.png", "banner_img_mb": "shopify://shop_images/Option_2.png", "content_alignment_h": "md:ts-items-center", "content_alignment_v": "md:ts-justify-center", "text_alignment": "center", "content_alignment_h_mobile": "ts-items-center", "content_alignment_v_mobile": "ts-justify-center", "text_alignment_mobile": "center", "top_spacing": 0, "bottom_spacing": 0, "top_spacing_mobile": 0, "bottom_spacing_mobile": 0}}, "multicolumn_4yj4Yz": {"type": "multicolumn", "blocks": {"column_h8hhtW": {"type": "column", "settings": {"media_type": "image", "image": "shopify://shop_images/Ecom_1068_jpg_ea792a4c-1c04-4a48-a972-70d21706f78b.webp", "media_url": "", "title": "BOLD, CULTURE-<PERSON><PERSON><PERSON> PIECES DESIGNED FOR EVERYDAY MOVEMENT AND EXPRESSION", "text": "", "link_label": "Shop Streetwear", "link": "shopify://collections/streetwear-shop-category-all"}}, "column_axDfwR": {"type": "column", "settings": {"media_type": "image", "image": "shopify://shop_images/image2_ce3f1342-eca5-4c72-8842-7ba2b0f3661b.heic", "media_url": "", "title": "ESSENTIAL ACCESSORIES AND STANDOUT FINDS TO COMPLETE YOUR LOOK", "text": "", "link_label": "Shop Things", "link": "shopify://collections/things-shop-category-all"}}}, "block_order": ["column_h8hhtW", "column_axDfwR"], "settings": {"title": "", "heading_size": "h1", "image_width": "half", "image_ratio": "square", "button_label": "", "button_link": "", "full_width": true, "overlay_content": true, "columns_desktop": 2, "column_alignment": "left", "background_style": "none", "color_scheme": "scheme-3", "columns_mobile": "1", "swipe_on_mobile": false, "padding_top": 0, "padding_bottom": 0}}, "multicolumn_Lq44g9": {"type": "multicolumn", "blocks": {"column_KQxmjM": {"type": "column", "settings": {"media_type": "image", "image": "shopify://shop_images/A58.jpg", "media_url": "shopify://blogs/news/bridging-🏾-connections", "title": "BRIDGING 🤝🏾 CONNECTIONS", "text": "", "link_label": "Read More", "link": "#"}}, "column_NCGRHd": {"type": "column", "settings": {"media_type": "image", "image": "shopify://shop_images/hero-ProGridTriumph4-d_6a7e0132-10eb-493e-96a3-515abf473c98.jpg", "media_url": "shopify://products/saucony-friends-progrid-triumph-4-cream-doe", "title": "New Arrival: <PERSON><PERSON><PERSON>Grid Triumph 4", "text": "", "link_label": "Shop Now", "link": "#"}}}, "block_order": ["column_KQxmjM", "column_NCGRHd"], "settings": {"title": "", "heading_size": "h1", "image_width": "half", "image_ratio": "square", "button_label": "", "button_link": "", "full_width": true, "overlay_content": true, "columns_desktop": 2, "column_alignment": "left", "background_style": "none", "color_scheme": "scheme-3", "columns_mobile": "1", "swipe_on_mobile": false, "padding_top": 0, "padding_bottom": 0}}, "mw_featured_collection_PD44cQ": {"type": "mw_featured_collection", "settings": {"color_scheme": "", "title": "Streetwear", "title_size": "ts-text-size_lg", "collection": "streetwear-new-arrivals", "view_all": true, "prodoct_limit": 12, "view_link_label": "Explore Collection", "text_alignment": "center", "view_all_style": "link", "container_width": 1920, "full_width": false, "box_color_scheme": "", "bordered": false, "image_ratio": "portrait", "image_shape": "default", "show_secondary_image": false, "show_vendor": false, "show_rating": false, "quick_add": "standard", "d_slides": 4, "l_slides": 3, "t_slides": 2, "m_slides": 2, "d_space": 0, "l_space": 0, "t_space": 0, "m_space": 10, "enable_loop": false, "center_mode": false, "mousewheel": true, "enable_autoplay": true, "autoplay_speed": 5000, "enable_navigation": true, "enable_pagination": false, "destroy_swiper": true, "pad_desk": 40, "pad_lap": 40, "pad_tab": 40, "pad_mob": 30}}, "rich_text_ENVRnj": {"type": "rich-text", "blocks": {"caption_N8bfYU": {"type": "caption", "settings": {"caption": "Explore New Arrivals from:", "text_style": "caption-with-letter-spacing", "text_size": "large"}}, "heading_mq7zBB": {"type": "heading", "settings": {"heading": "<a href=\"/collections/sneakers-converse\" title=\"Converse\">Converse</a>, <a href=\"/collections/jordan-1\" title=\"JORDAN\"><PERSON></a>, <a href=\"/collections/things-shop-brand-makeway\" title=\"Makeway\">Makeway</a>, <a href=\"/collections/nike\" title=\"Nike\">Nike</a>, <a href=\"/collections/sneakers-puma\" title=\"Puma\">Puma</a>, <a href=\"/collections/womens-reebok\" title=\"Reebok\">Reebok</a>, <a href=\"/collections/sneakers-shop-brand-ugg\" title=\"UGG\">Ugg</a>", "heading_size": "hxxl"}}}, "block_order": ["caption_N8bfYU", "heading_mq7zBB"], "disabled": true, "custom_css": ["h2.rich-text__heading {font-family: var(--font-body-family); font-weight: 500; font-size: clamp(3rem, 8vw, 6.5rem); line-height: 1.6;}", ".rich-text__blocks a {text-decoration: underline 3px; text-underline-offset: 1rem;}", "@media screen and (min-width: 990px) {.rich-text__blocks {max-width: 135rem; margin: 0 auto; }}", "@media only screen and (max-width: 767px) {.rich-text__wrapper {width: 100%; }}"], "settings": {"desktop_content_position": "left", "content_alignment": "left", "color_scheme": "scheme-3", "full_width": true, "padding_top": 100, "padding_bottom": 100}}, "multirow_EDcHQc": {"type": "multirow", "blocks": {"row_cagGYG": {"type": "row", "settings": {"image": "shopify://shop_images/7_58a06e4a-4efd-4530-94ac-c22bff91645a.png", "caption": "Explore New Arrivals from:", "heading": "", "text": "<h2><a href=\"/collections/salomon\" title=\"Salomon\">Salomon</a></h2><h2><a href=\"/collections/jordan\" title=\"Jordan\">Jordan</a></h2><h2><a href=\"/collections/makeway\" title=\"Makeway\">Makeway</a></h2><h2><a href=\"/collections/nike\" title=\"Nike\">Nike</a></h2><h2><a href=\"/collections/sneakers-puma\" title=\"Puma\">Puma</a></h2><h2><a href=\"/collections/sneakers-shop-brand-ugg\" title=\"UGG\">Ugg</a></h2><h2><a href=\"/collections/new-balance\" title=\"New Balance\">New Balance</a></h2><h2><a href=\"/collections/saucony\" title=\"Saucony\">Saucony</a></h2>", "button_label": "", "button_link": ""}}}, "block_order": ["row_cagGYG"], "custom_css": [".image-with-text__text.rte.body {margin-top: clamp(2rem, 3vw, 3rem); width: 100%;}", ".image-with-text__text.rte.body h2 a {font-family: var(--font-body-family); font-size: clamp(2rem, 3vw, 2.5rem); display: block; padding-bottom: 0.8rem; margin-bottom: 0.8rem; text-decoration: none; border-bottom: solid 1px #222;}", ".image-with-text__text.rte.body h2 a:hover {border-color: #ffffff;}"], "settings": {"image_height": "adapt", "desktop_image_width": "medium", "image_layout": "alternate-right", "full_width": true, "heading_size": "hxxl", "text_style": "body", "button_style": "secondary", "desktop_content_position": "middle", "desktop_content_alignment": "left", "mobile_content_alignment": "left", "section_color_scheme": "", "row_color_scheme": "scheme-4", "padding_top": 0, "padding_bottom": 0}}, "image_banner_mq8kVN": {"type": "image-banner", "settings": {"image": "shopify://shop_images/store_banner.jpg", "image_overlay_opacity": 0, "image_height": "adapt", "image_behavior": "none", "desktop_content_position": "middle-center", "desktop_content_alignment": "center", "show_text_box": true, "color_scheme": "", "stack_images_on_mobile": true, "mobile_content_alignment": "center", "show_text_below": true}}}, "order": ["slideshow_r9aHjb", "mw_featured_collection_9T79W4", "multicolumn_7V79mm", "mw_banner_NANQWY", "multicolumn_4yj4Yz", "multicolumn_Lq44g9", "mw_featured_collection_PD44cQ", "rich_text_ENVRnj", "multirow_EDcHQc", "image_banner_mq8kVN"]}