.newsletter-form {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  position: relative;
}

@media screen and (min-width: 750px) {
  .newsletter-form {
    align-items: flex-start;
    max-width: 40rem;
  }
}

.newsletter-form__field-wrapper {
  width: 100%;
}
.newsletter-form__field-wrapper .field--wrapper {
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
}
.newsletter-form__field-wrapper .field__input {
  flex: 1;
  padding: 1rem 0;
  outline: none !important;
  box-shadow: none !important;
  border-bottom: 1px solid rgb(var(--color-foreground));
}
.newsletter-form__field-wrapper .field__input::placeholder {
  opacity: 0.8;
  color: rgb(var(--color-foreground));
}

.newsletter-form__field-wrapper .field {
  z-index: 0;
}

.newsletter-form__message {
  justify-content: center;
  margin-bottom: 0;
}

.newsletter-form__message--success {
  margin-top: 2rem;
}

@media screen and (min-width: 750px) {
  .newsletter-form__message {
    justify-content: flex-start;
  }
}

.newsletter-form__button {
  position: relative;
  width: 10rem;
  margin: 0;
  right: 0;
  top: 0;
  height: 100%;
  z-index: 2;
}
button.newsletter-form__button:hover {
  color: rgb(var(--color-background)) !important;
}

.newsletter-form__button:focus-visible {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0 0.4rem rgba(var(--color-foreground));
  background-color: rgb(var(--color-background));
}

.newsletter-form__button:focus {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0 0.4rem rgba(var(--color-foreground));
  background-color: rgb(var(--color-background));
}

.newsletter-form__button .icon {
  width: 1.5rem;
}
