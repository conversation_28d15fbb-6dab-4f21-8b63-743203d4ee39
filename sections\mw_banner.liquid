{%- assign setting = section.settings -%}

{% style %}
	#shopify-section-{{ section.id }} {
		--text-align: {{ setting.text_alignment_mobile | default: 'center' }};
		--overlay-opacity: {{ setting.overlay_opacity | default: 0.5 }};
		{% if setting.top_spacing != 0 and setting.top_spacing_mobile != 0 -%}
			--top-spacing: clamp({{ setting.top_spacing_mobile }}rem, 10vw, {{ setting.top_spacing }}rem);
		{% else %}
			--top-spacing: 0;
		{%- endif %}
		{%- if setting.bottom_spacing != 0 and setting.bottom_spacing_mobile != 0 -%}
			--bottom-spacing: clamp({{ setting.bottom_spacing_mobile }}rem, 10vw, {{ setting.bottom_spacing }}rem);
		{% else %}
			--bottom-spacing: 0;
		{%- endif -%}
	}

	{% if section.index == 1 -%}
		@media only screen and (min-width: 1200px) {
			.page-main-banner {
				height: calc(100dvh - 6vw);
			}
		}
	{%- endif %}

	@media only screen and (min-width: 768px) {
		#shopify-section-{{ section.id }} {
			--text-align: {{ setting.text_alignment | default: 'center' }};
		}
	}

	{% if setting.banner_img_mb != blank %}
		@media only screen and (max-width: 767px) {
			.media-banner {
				aspect-ratio: {{ setting.banner_img_mb.aspect_ratio }} !important;
			}
		}
	{% endif %}
{% endstyle %}

<div class="color-{{ section.settings.color_scheme }} media-banner ts-relative ts-w-full md:ts-aspect-video ts-aspect-[3/4] ts-overflow-hidden ts-grid ts-grid-rows-1 ts-grid-cols-1{% if section.index == 1 %} page-main-banner{% endif %}">
	{%- if setting.add_overlay -%}
		<div class="overlay ts-relative ts-z-[1] ts-row-span-full ts-col-span-full ts-bg-black ts-opacity-[var(--overlay-opacity)] ts-pointer-events-none">&nbsp;</div>
	{%- endif -%}

	<div class="banner-media banner-media--{{ setting.banner_type }} ts-relative ts-w-full ts-h-auto ts-row-span-full ts-col-span-full ts-pointer-events-none">
		{%- if setting.banner_type == 'video' and setting.video != blank -%}
			{%- if setting.poster != blank -%}
				{% assign poster = setting.poster | image_url: width: setting.poster.width %}
			{%- endif -%}
			{%- assign video_source = setting.video.sources[1].url -%}
			<video class="lazy video-player ts-w-full ts-h-full ts-object-cover" autoplay muted loop playsinline poster="{{ poster }}">
				<source data-src="{{ video_source }}" type="video/mp4">
				Your browser does not support the video tag.
			</video>
		{%- elsif setting.banner_type == 'image' and setting.banner_image != blank -%}
			{%- liquid
				assign image = setting.banner_image
				assign widths = '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'
				assign image_class = 'motion-reduce ts-w-full ts-max-w-full ts-h-auto'
				assign fetch_priority = 'auto'
				assign mobile_image = image | image_url: width: image.width

				if section.index == 1
					assign fetch_priority = 'high'
				endif

				if setting.banner_img_mb != blank
					assign mobile_image = setting.banner_img_mb | image_url: width: setting.banner_img_mb.width
				endif
			-%}

			<div class="banner-img-wrapper ts-w-full ts-h-full ts-overflow-hidden">
				<picture class="media ts-w-full ts-h-full">
						<source srcset="{{ mobile_image }}" media="(max-width: 767px)"/>
						{{
							image
							| image_url: width: 3840
							| image_tag: height: image.height, sizes: sizes, widths: widths, fetchpriority: fetch_priority, class: image_class, alt: image.alt | default: 'Banner Image'
						}}
					</picture>
			</div>
		{%- endif -%}
	</div>

	<div class="banner-contents ts-relative ts-z-[2] ts-flex ts-flex-col {{ setting.content_alignment_h }} {{ setting.content_alignment_h_mobile }} {{ setting.content_alignment_v }} {{ setting.content_alignment_v_mobile }} ts-pt-[var(--top-spacing)] ts-pb-[var(--bottom-spacing)] text-align ts-row-span-full ts-col-span-full ts-px-8">
		<div class="contents-wrapper ts-w-auto md:ts-p-8 ts-p-4{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
			{%- for block in section.blocks -%}
				{% case block.type %}
					{% when 'heading' %}
						{%- if block.settings.type == 'title' -%}
							<h2 class="{{ block.settings.text_transform }} {{ block.settings.heading_size }} ts-leading-none ts-font-normal" {{ block.shopify_attributes }}>
								{{ block.settings.heading_text }}
							</h2>
						{% elsif block.settings.type == 'body' %}
								<p class="{{ block.settings.text_transform }} {{ block.settings.heading_size }}" {{ block.shopify_attributes }}>{{ block.settings.heading_text }}</p>
						{% elsif block.settings.type == 'caption' %}
								<p class="caption {{ block.settings.text_transform }} ts-text-size_sm" {{ block.shopify_attributes }}>{{ block.settings.heading_text }}</p>
						{%- endif -%}
					{% when 'richtext' %}
						<div class="rte ts-mt-6" {{ block.shopify_attributes }}>{{ block.settings.richtext_content }}</div>
				{% endcase %}
			{%- endfor -%}

			<div class="btn-wrapper ts-inline-block ts-space-x-10 ts-w-full ts-mt-8 ts-align-middle ts-px-5">
				{%- for block in section.blocks -%}
					{%- if block.type == 'buttons' -%}
						{%- if block.settings.button_text != blank -%}
							<a href="{{ block.settings.button_url | default: '#' }}" class="button button--primary hover:!ts-text-[rgba(var(--color-foreground))] hover:!ts-bg-[rgba(var(--color-background))] hover:after:!ts-shadow-none" {{ block.shopify_attributes }}>
								{{ block.settings.button_text }}
							</a>
						{%- endif -%}
					{%- endif -%}
				{%- endfor -%}
			</div>
		</div>
	</div> 
</div>

{%- if setting.mb_video != blank -%}
  {% assign poster_mob = setting.mb_poster | image_url: width: setting.mb_poster.width %}
  {% if setting.mb_poster == blank %}
    {% assign poster_mob = setting.poster | image_url: width: setting.poster.width %}
  {% endif %}
	
  <script type="text/javascript" async>
    document.addEventListener("DOMContentLoaded", function() {
      const loadMobileMedia = () => {
        let mobileView = window.matchMedia('(min-width: 0px) and (max-width: 767px)');

        if(!mobileView.matches) {
          return
        } else {
          let mb_video = '{{ setting.mb_video.sources[1].url }}',
              mb_poster = '{{ poster_mob }}',
              video_player = document.querySelector("video.video-player");


          video_player.children[0].dataset.src = mb_video
          video_player.poster = mb_poster
        }
      }

      loadMobileMedia();
      window.addEventListener('resize', loadMobileMedia);
    });
  </script>
{%- endif -%}

{% schema %}
	{
		"name": "[MW] Banner",
		"class": "mw-banner",
		"tag": "section",
		"settings": [
			{
				"type": "color_scheme",
				"id": "color_scheme",
				"label": "t:sections.all.colors.label",
				"default": "scheme-1"
			},
			{
				"type": "select",
				"id": "banner_type",
				"label": "Banner Type",
				"options": [
					{
						"value": "image",
						"label": "Image"
					},
					{
						"value": "video",
						"label": "Video"
					}
				],
				"default": "image"
			},
			{
        "type": "checkbox",
        "id": "add_overlay",
        "label": "Show Overlay",
        "default": false
      },
			{
				"type": "range",
				"id": "overlay_opacity",
				"label": "Overlay Opacity",
				"default": 0.5,
				"min": 0,
				"max": 1,
				"step": 0.1
			},
			{
				"type": "header",
				"content": "Image Banner"
			},
			{
				"type": "image_picker",
				"id": "banner_image",
				"label": "Banner Image",
				"info": "Desktop"
			},
			{
				"type": "image_picker",
				"id": "banner_img_mb",
				"label": "Banner Image",
				"info": "Mobile"
			},
			{
				"type": "header",
				"content": "Video Banner"
			},
			{
        "type": "video",
        "id": "video",
        "label": "Banner Video",
        "info": "A Shopify-hosted video"
      },
      {
        "type": "image_picker",
        "id": "poster",
        "label": "Banner Video Poster",
        "info": "Video fallback image"
      },
			{
        "type": "header",
        "content": "Mobile Video Banner"
      },
      {
        "type": "video",
        "id": "mb_video",
        "label": "Mobile Banner Video",
        "info": "A Shopify-hosted video"
      },
      {
        "type": "image_picker",
        "id": "mb_poster",
        "label": "Mobile Banner Video Poster",
        "info": "Video fallback image"
      },
			{
				"type": "header",
				"content": "Content Settings"
			},
			{
				"type": "select",
				"id": "content_alignment_h",
				"label": "Content Alignment",
				"info": "Horizontal alignment of the contents.",
				"options": [
					{
						"value": "md:ts-items-start",
						"label": "Left"
					},
					{
						"value": "md:ts-items-center",
						"label": "Center"
					},
					{
						"value": "md:ts-items-end",
						"label": "Right"
					}
				],
				"default": "md:ts-items-center"
			},
			{
				"type": "select",
				"id": "content_alignment_v",
				"label": "Content Alignment",
				"info": "Vertical alignment of the contents.",
				"options": [
					{
						"value": "md:ts-justify-start",
						"label": "Top"
					},
					{
						"value": "md:ts-justify-center",
						"label": "Middle"
					},
					{
						"value": "md:ts-justify-end",
						"label": "Bottom"
					}
				],
				"default": "md:ts-justify-center"
			},
			{
				"type": "text_alignment",
				"id": "text_alignment",
				"label": "Text Alignment",
				"default": "center"
			},
			{
				"type": "header",
				"content": "Mobile Content Settings"
			},
			{
				"type": "select",
				"id": "content_alignment_h_mobile",
				"label": "Content Alignment",
				"info": "Horizontal alignment of the contents.",
				"options": [
					{
						"value": "ts-items-start",
						"label": "Left"
					},
					{
						"value": "ts-items-center",
						"label": "Center"
					},
					{
						"value": "ts-items-end",
						"label": "Right"
					}
				],
				"default": "ts-items-center"
			},
			{
				"type": "select",
				"id": "content_alignment_v_mobile",
				"label": "Content Alignment",
				"info": "Vertical alignment of the contents.",
				"options": [
					{
						"value": "ts-justify-start",
						"label": "Top"
					},
					{
						"value": "ts-justify-center",
						"label": "Middle"
					},
					{
						"value": "ts-justify-end",
						"label": "Bottom"
					}
				],
				"default": "ts-justify-center"
			},
			{
				"type": "text_alignment",
				"id": "text_alignment_mobile",
				"label": "Text Alignment",
				"default": "center"
			},
			{
				"type": "header",
				"content": "Spacing",
				"info": "Top and bottom spacing."
			},
			{
				"type": "range",
				"id": "top_spacing",
				"label": "Top Spacing",
				"default": 0,
				"min": 0,
				"max": 25,
				"step": 0.5,
				"unit": "rem"
			},
			{
				"type": "range",
				"id": "bottom_spacing",
				"label": "Bottom Spacing",
				"default": 0,
				"min": 0,
				"max": 25,
				"step": 0.5,
				"unit": "rem"
			},
			{
				"type": "range",
				"id": "top_spacing_mobile",
				"label": "Mobile Top Spacing",
				"default": 0,
				"min": 0,
				"max": 10,
				"step": 0.1,
				"unit": "rem"
			},
			{
				"type": "range",
				"id": "bottom_spacing_mobile",
				"label": "Mobile Bottom Spacing",
				"default": 0,
				"min": 0,
				"max": 10,
				"step": 0.1,
				"unit": "rem"
			}
		],
		"blocks": [
			{
				"type": "heading",
				"name": "Heading",
				"limit": 3,
				"settings": [
					{
						"type": "select",
						"id": "type",
						"label": "Type",
						"options": [
							{
								"value": "title",
								"label": "Title"
							},
							{
								"value": "body",
								"label": "Body"
							},
							{
								"value": "caption",
								"label": "Caption"
							}
						],
						"default": "title"
					},
					{
						"type": "inline_richtext",
						"id": "heading_text",
						"label": "Heading Text",
						"default": "Banner Heading"
					},
					{
						"type": "select",
						"id": "heading_size",
						"label": "Heading Size",
						"options": [
							{
								"value": "ts-text-clamp_xxl",
								"label": "2X Large"
							},
							{
								"value": "ts-text-clamp_xl",
								"label": "Extra Large"
							},
							{
								"value": "ts-text-clamp_l",
								"label": "Large"
							},
							{
								"value": "ts-text-size_lg",
								"label": "Medium"
							},
							{
								"value": "ts-text-size_md",
								"label": "Small"
							},
							{
								"value": "ts-text-size_sm",
								"label": "Extra Small"
							}
						],
						"default": "ts-text-clamp_xxl"
					},
					{
						"type": "select",
						"id": "text_transform",
						"label": "Text Transform",
						"options": [
							{
								"value": "ts-uppercase",
								"label": "Uppercase"
							},
							{
								"value": "ts-capitalize",
								"label": "Capitalize"
							},
							{
								"value": "ts-lowercase",
								"label": "Lowercase"
							},
							{
								"value": "ts-normal-case",
								"label": "None"
							}
						],
						"default": "ts-uppercase"
					}
				]
			},
			{
				"type": "richtext",
				"name": "Rich Text",
				"limit": 1,
				"settings": [
					{
						"type": "richtext",
						"id": "richtext_content",
						"label": "Rich Text Content",
						"default": "<p>Rich Text Content</p>"
					}
				]
			},
			{
				"type": "buttons",
				"name": "Button",
				"limit": 2,
				"settings": [
					{
						"type": "url",
						"id": "button_url",
						"label": "Button URL"
					},
					{
						"type": "text",
						"id": "button_text",
						"label": "Button Text",
						"default": "Button"
					}
				]
			}
		],
		"presets": [
			{
				"name": "Banner",
				"blocks": [
					{ "type": "heading" },
					{ "type": "buttons" },
					{ "type": "buttons" }
				]
			}
		]
	}
{% endschema %}