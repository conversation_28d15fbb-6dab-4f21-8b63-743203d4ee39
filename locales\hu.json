{"general": {"password_page": {"login_form_heading": "Belépés a webáruházba jelszóval:", "login_password_button": "Belépés j<PERSON>", "login_form_password_label": "Je<PERSON><PERSON><PERSON>", "login_form_password_placeholder": "<PERSON><PERSON><PERSON><PERSON>", "login_form_error": "<PERSON><PERSON><PERSON> be.", "login_form_submit": "Belépés", "admin_link_html": "Te vagy a webáruh<PERSON>z tula<PERSON>? <a href=\"/admin\" class=\"link underlined-link\">Itt tudsz bejelentkezni</a>", "powered_by_shopify_html": "A bolt szolgáltatója a {{ shopify }}"}, "social": {"alt_text": {"share_on_facebook": "Megosztás a Facebookon", "share_on_twitter": "Megosztás az X-en", "share_on_pinterest": "Közzététel a Pinteresten"}, "links": {"twitter": "X (Twitter)", "facebook": "Facebook", "pinterest": "Pinterest", "instagram": "Instagram", "tumblr": "Tumblr", "snapchat": "Snapchat", "youtube": "YouTube", "vimeo": "Vimeo", "tiktok": "TikTok"}}, "continue_shopping": "Vásárlás folytat<PERSON>", "pagination": {"label": "Tördelés", "page": "{{ number }}. oldal", "next": "Következő oldal", "previous": "Előző oldal"}, "search": {"search": "Keresés", "reset": "Keresőszó törlése"}, "cart": {"view": "<PERSON><PERSON><PERSON><PERSON> ({{ count }})", "item_added": "Betettük a terméket a kosárba", "view_empty_cart": "<PERSON><PERSON><PERSON><PERSON>"}, "share": {"copy_to_clipboard": "Hivatkozás másolása", "share_url": "Hivatkozás", "success_message": "A vágólapra másoltuk a hivatkozást.", "close": "Megosztás befejezése"}, "slider": {"of": "/", "next_slide": "Következő dia", "previous_slide": "Előző dia", "name": "Csúszka"}}, "newsletter": {"label": "E-mail-cím", "success": "Köszönjük a feliratkozást", "button_label": "Felirat<PERSON>z<PERSON>"}, "accessibility": {"skip_to_text": "Ugrás a tartalomhoz", "close": "Bezárás", "unit_price_separator": "/", "vendor": "Forgalmazó:", "error": "Hiba", "refresh_page": "Ha kiválasztasz egy lehetőséget, a teljes oldal frissül.", "link_messages": {"new_window": "Tartalom megnyitása új a<PERSON>kban.", "external": "Külső webhelyet nyit meg."}, "loading": "Betöltés folyamatban…", "skip_to_product_info": "<PERSON><PERSON><PERSON><PERSON>, és ugrás a termékadatokra", "total_reviews": "összes értékelés", "star_reviews_info": "{{ rating_max }}/{{ rating_value }} csillag", "collapsible_content_title": "Összecsukható tartalom", "complementary_products": "Kiegészítő termékek"}, "blogs": {"article": {"blog": "Blog", "read_more_title": "Tov<PERSON><PERSON><PERSON><PERSON>: {{ title }}", "comments": {"one": "{{ count }} hozzászólás", "other": "{{ count }} hozzászólás"}, "moderated": "Felhívjuk a figyelmedet, hogy a hozzászólásokat jóvá kell hagyni a közzétételük előtt.", "comment_form_title": "Hozzászólás írása", "name": "Név", "email": "E-mail-cím", "message": "Hozzászólás", "post": "Hozzászólás elküldése", "back_to_blog": "<PERSON><PERSON><PERSON> a blogba", "share": "Cikk megosztása", "success": "Elküldtük a hozzászólásodat. Köszönjük!", "success_moderated": "Elküldtük a hozzászólásodat. Blogunkat moderáljuk, ez<PERSON>rt egy kis idő múlva tesszük csak közzé a hozzászólást."}}, "onboarding": {"product_title": "<PERSON><PERSON><PERSON>", "collection_title": "<PERSON><PERSON><PERSON><PERSON><PERSON> neve"}, "products": {"product": {"add_to_cart": "Hozzáadás a kosárhoz", "description": "Le<PERSON><PERSON><PERSON>", "on_sale": "<PERSON><PERSON><PERSON><PERSON>", "product_variants": "Termékváltozatok", "quantity": {"label": "Mennyiség", "input_label": "{{ product }} mennyisége", "increase": "{{ product }} mennyiségének növelése", "decrease": "{{ product }} mennyiségének csökkentése", "minimum_of": "Minimum: {{ quantity }}", "maximum_of": "Maximum: {{ quantity }}", "multiples_of": "Növekvés: {{ quantity }}", "in_cart_html": "Kosárban lévő mennyiség: <span class=\"quantity-cart\">{{ quantity }}</span>", "note": "Mennyiségi szabályok megtekintése", "min_of": "Min. {{ quantity }}", "max_of": "Max. {{ quantity }}"}, "price": {"from_price_html": "Legalacson<PERSON><PERSON> ár: {{ price }}", "regular_price": "<PERSON><PERSON><PERSON><PERSON>", "sale_price": "<PERSON><PERSON><PERSON><PERSON>", "unit_price": "<PERSON>gy<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "share": "A termék megosztása", "sold_out": "Elfogyott", "unavailable": "<PERSON><PERSON><PERSON>", "vendor": "Forgalmazó", "video_exit_message": "{{ title }}: a teljes k<PERSON> videó ugyanabban az ablakban nyílik meg.", "xr_button": "Megtekintés a saját környezetben", "xr_button_label": "Megtekintés a saját környezetben: kiterjesztettvalóság-alapú ablakban töltődik be az elem", "pickup_availability": {"view_store_info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "check_other_stores": "<PERSON><PERSON>tó más webáruházakban?", "pick_up_available": "Személyesen átvehető", "pick_up_available_at_html": "Személyesen átvehető itt: <span class=\"color-foreground\">{{ location_name }}</span>", "pick_up_unavailable_at_html": "Személyesen egyelőre nem vehető át itt: <span class=\"color-foreground\">{{ location_name }}</span>", "unavailable": "<PERSON><PERSON> betölteni az átvehetőségi adatokat", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "media": {"open_media": "{{ index }}. médiafájl megnyitása a modális párb<PERSON>z<PERSON>dpanelen", "play_model": "Lejátszás a 3D-megjelenítőben", "play_video": "<PERSON><PERSON><PERSON>", "gallery_viewer": "Galériamegjelenítő", "load_image": "{{ index }}. kép betöltése galérianézetben", "load_model": "{{ index }}. térhatású modell betöltése galérianézetben", "load_video": "{{ index }}. v<PERSON><PERSON> le<PERSON>a galérianézetben", "image_available": "{{ index }}. kép betöltve galérianézetben"}, "view_full_details": "Minden részlet megtekintése", "shipping_policy_html": "A <a href=\"{{ link }}\">szállítási költséget</a> a megrendeléskor számítjuk ki.", "choose_options": "Válassz a lehetőségek közül", "choose_product_options": "Termékváltozatok – {{ product_name }}", "value_unavailable": "{{ option_value }} – <PERSON><PERSON><PERSON>", "variant_sold_out_or_unavailable": "A változat elfogyott vagy nincs k<PERSON>ten", "inventory_in_stock": "<PERSON><PERSON><PERSON><PERSON>", "inventory_in_stock_show_count": "{{ quantity }} r<PERSON><PERSON><PERSON><PERSON>", "inventory_low_stock": "<PERSON><PERSON><PERSON><PERSON>", "inventory_low_stock_show_count": "Alacsony <PERSON>: csak {{ quantity }} van r<PERSON>", "inventory_out_of_stock": "<PERSON><PERSON><PERSON>", "inventory_out_of_stock_continue_selling": "<PERSON><PERSON><PERSON><PERSON>", "sku": "Termékváltozat", "volume_pricing": {"title": "Mennyis<PERSON><PERSON>", "note": "Mennyiségi <PERSON> rendelkezésre <PERSON>", "minimum": "{{ quantity }}+", "price_range": "{{ minimum }} – {{ maximum }}", "price_at_each_html": "{{ price }}/db"}, "taxes_included": "Tartalmazza az adókat.", "duties_included": "Tartalmazza a vámokat.", "duties_and_taxes_included": "Tartalmazza a vámokat és az adókat."}, "modal": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "facets": {"apply": "Alkalmaz", "clear": "Törlés", "clear_all": "Az összes eltávolítása", "from": "Ettől:", "filter_and_sort": "Szűrés és rendezés", "filter_by_label": "Szűrés:", "filter_button": "Szűrés", "filters_selected": {"one": "{{ count }} elem k<PERSON>", "other": "{{ count }} elem k<PERSON>"}, "max_price": "A legmagasabb ár {{ price }}", "product_count": {"one": "{{ count }}/{{ product_count }} termék", "other": "{{ count }}/{{ product_count }} termék"}, "product_count_simple": {"one": "{{ count }} term<PERSON>k", "other": "{{ count }} term<PERSON>k"}, "reset": "Alaphelyzet", "sort_button": "Rendez<PERSON>", "sort_by_label": "Rendez<PERSON><PERSON> szempont:", "to": "Eddig:", "clear_filter": "Szűrő eltávolítása", "filter_selected_accessibility": "{{ type }} ({{ count }} szűrő kiválasztva)", "show_more": "<PERSON><PERSON><PERSON>", "show_less": "<PERSON><PERSON><PERSON><PERSON>", "filter_and_operator_subtitle": "Az összesnek megfelelő"}}, "templates": {"search": {"no_results": "<PERSON><PERSON><PERSON> ta<PERSON> erre: {{ terms }}. <PERSON><PERSON><PERSON><PERSON> a <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vagy írj be egy másik szót vagy k<PERSON>.", "results_with_count": {"one": "{{ count }} ta<PERSON><PERSON><PERSON>", "other": "{{ count }} ta<PERSON><PERSON><PERSON>"}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON>", "products": "Termékek", "search_for": "Ke<PERSON>és erre: {{ terms }}", "results_with_count_and_term": {"one": "{{ count }} ta<PERSON><PERSON><PERSON> erre: {{ terms }}", "other": "{{ count }} ta<PERSON><PERSON><PERSON> erre: {{ terms }}"}, "results_pages_with_count": {"one": "{{ count }} oldal", "other": "{{ count }} oldal"}, "results_suggestions_with_count": {"one": "{{ count }} javaslat", "other": "{{ count }} javaslat"}, "results_products_with_count": {"one": "{{ count }} term<PERSON>k", "other": "{{ count }} term<PERSON>k"}, "suggestions": "Javaslatok", "pages": "<PERSON><PERSON>"}, "cart": {"cart": "<PERSON><PERSON><PERSON><PERSON>"}, "contact": {"form": {"name": "Név", "email": "E-mail-cím", "phone": "Telefonszám", "comment": "Hozzászólás", "send": "<PERSON><PERSON><PERSON><PERSON>", "post_success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy írtál nekünk. A lehető legrövidebb időn belül válaszolni fogunk.", "error_heading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, helyesbítsd a következőket:", "title": "Kapcsolattartói űrlap"}}, "404": {"title": "<PERSON>em <PERSON>l<PERSON>l<PERSON> az oldalt", "subtext": "404-es hiba t<PERSON>."}}, "sections": {"header": {"announcement": "<PERSON><PERSON><PERSON><PERSON>", "menu": "<PERSON><PERSON>", "cart_count": {"one": "{{ count }} elem", "other": "{{ count }} elem"}}, "cart": {"title": "<PERSON><PERSON><PERSON><PERSON>", "caption": "Kosárban lévő termékek", "remove_title": "{{ title }} eltávolítása", "note": "Megjegyzések a rendeléssel kapcsolatban", "checkout": "Megrendelés", "empty": "A kosarad üres", "cart_error": "Hiba történt a kosár frissítése közben. Próbálkozz újra.", "cart_quantity_error_html": "Ebb<PERSON>l a termékb<PERSON>l legfeljebb {{ quantity }} darabot rakhatsz a kosárba.", "headings": {"product": "Termék", "price": "<PERSON><PERSON>", "total": "Végösszeg", "quantity": "Mennyiség", "image": "Termék <PERSON>"}, "update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "login": {"title": "<PERSON><PERSON><PERSON>?", "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\">Jelentkezz be</a> a gyorsabb fizetéshez."}, "estimated_total": "Becsült végösszeg", "new_estimated_total": "Új becsült végösszeg", "duties_and_taxes_included_shipping_at_checkout_with_policy_html": "Tartalmazza a vámokat és az adókat. A kedvezményeket és a <a href=\"{{ link }}\">szállítási költséget</a> a megrendeléskor számítjuk ki.", "duties_and_taxes_included_shipping_at_checkout_without_policy": "Tartalmazza a vámokat és az adókat. A kedvezményeket és a szállítási költséget a megrendeléskor számítjuk ki.", "taxes_included_shipping_at_checkout_with_policy_html": "Tartalmazza az adókat. A kedvezményeket és a <a href=\"{{ link }}\">szállítási költséget</a> a megrendeléskor számítjuk ki.", "taxes_included_shipping_at_checkout_without_policy": "Tartalmazza az adókat. A kedvezményeket és a szállítási költséget a megrendeléskor számítjuk ki.", "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_html": "Tartalmazza a vámokat. <PERSON><PERSON> ad<PERSON>, a kedvezményeket és a <a href=\"{{ link }}\">szállítási költséget</a> a megrendeléskor számítjuk ki.", "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy": "Tartalmazza a vámokat. <PERSON><PERSON>, a kedvezményeket és a szállítási költséget a megrendeléskor számítjuk ki", "taxes_at_checkout_shipping_at_checkout_with_policy_html": "<PERSON><PERSON>, a kedvezményeket és a <a href=\"{{ link }}\">szállítási költséget</a> a megrendeléskor számítjuk ki.", "taxes_at_checkout_shipping_at_checkout_without_policy": "<PERSON><PERSON>, a kedvezményeket és a szállítási költséget a megrendeléskor számítjuk ki"}, "footer": {"payment": "Fizetési m<PERSON>"}, "featured_blog": {"view_all": "Az összes megtekintése", "onboarding_title": "Blogbejegyzés", "onboarding_content": "Itt foglalhatod össze a vásárlóidnak, mir<PERSON>l szól a blogbejegyzésed"}, "featured_collection": {"view_all": "Az összes megtekintése", "view_all_label": "<PERSON>z ebben a kollekcióban szereplő összes termék megtekintése: {{ collection_name }}"}, "collection_list": {"view_all": "Az összes megtekintése"}, "collection_template": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON>", "use_fewer_filters_html": "<PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON> <a class=\"{{ class }}\" href=\"{{ link }}\">távolítsd el az összeset</a>."}, "video": {"load_video": "<PERSON><PERSON><PERSON> betölt<PERSON>: {{ description }}"}, "slideshow": {"load_slide": "Dia betöltése", "previous_slideshow": "Előző dia", "next_slideshow": "Következő dia", "pause_slideshow": "Diavetítés megállítása", "play_slideshow": "Diavetítés indítása", "carousel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slide": "<PERSON>a"}, "page": {"title": "<PERSON><PERSON> címe"}, "announcements": {"previous_announcement": "Előző közlemény", "next_announcement": "Következő közlemény", "carousel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "announcement": "<PERSON><PERSON><PERSON><PERSON>", "announcement_bar": "<PERSON><PERSON><PERSON><PERSON>áv"}, "quick_order_list": {"product_total": "Termék részösszege", "view_cart": "<PERSON><PERSON><PERSON><PERSON>", "each": "{{ money }}/db", "product": "Termék", "variant": "Változat", "variant_total": "Változat összesen", "items_added": {"one": "{{ quantity }} term<PERSON><PERSON>", "other": "{{ quantity }} term<PERSON><PERSON>"}, "items_removed": {"one": "{{ quantity }} term<PERSON><PERSON>", "other": "{{ quantity }} term<PERSON><PERSON>"}, "product_variants": "Termékváltozatok", "total_items": "Összes termék", "remove_all_items_confirmation": "Eltávolítod mind a(z) {{ quantity }} terméket a kosárból?", "remove_all": "Az összes eltávolítása", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "remove_all_single_item_confirmation": "Eltávolítod ezt a(z) 1 terméket a kosárból?", "min_error": "A termék minimális mennyisége {{ min }}", "max_error": "A termék maximális mennyisége {{ max }}", "step_error": "A termék mennyisége csak {{ step }} darabos lépésekben növelhető"}}, "localization": {"country_label": "Ország/régió", "language_label": "Nyelv", "update_language": "Nyelv módosítása", "update_country": "Orsz<PERSON>g/ré<PERSON><PERSON> f<PERSON>", "search": "Keresés", "popular_countries_regions": "Népszerű országok/régiók", "country_results_count": "{{ count }} ország/ré<PERSON><PERSON>"}, "customer": {"account": {"title": "<PERSON>ók", "details": "Fiókadatok", "view_addresses": "Címek megtekintése", "return": "Vissza a fiókadatokhoz"}, "account_fallback": "<PERSON>ók", "activate_account": {"title": "Fiók aktiválása", "subtext": "A fiók aktiválásához hozz létre egy jelszót.", "password": "Je<PERSON><PERSON><PERSON>", "password_confirm": "<PERSON><PERSON><PERSON>ó megerősítése", "submit": "Fiók aktiválása", "cancel": "Meghívás elutasítása"}, "addresses": {"title": "Címek", "default": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "add_new": "<PERSON>j cím ho<PERSON>", "edit_address": "<PERSON><PERSON><PERSON>", "first_name": "U<PERSON><PERSON><PERSON>v", "last_name": "Vezetéknév", "company": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "address1": "1. c<PERSON>m", "address2": "2. c<PERSON><PERSON>", "city": "Település", "country": "Ország/régió", "province": "<PERSON><PERSON>", "zip": "Irányí<PERSON>", "phone": "Telefonszám", "set_default": "Beállítás alapértelmezett címk<PERSON>t", "add": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Szerkesztés", "delete": "Törlés", "delete_confirm": "<PERSON><PERSON><PERSON>, hogy törlöd a címet?"}, "log_in": "Bejelentkezés", "log_out": "Kijelentkezés", "login_page": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "create_account": "Fiók létrehozása", "email": "E-mail-cím", "forgot_password": "<PERSON><PERSON><PERSON>j<PERSON>tted a jelszavadat?", "guest_continue": "<PERSON><PERSON><PERSON><PERSON>", "guest_title": "Folytatás vendégk<PERSON>t", "password": "Je<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sign_in": "Bejelentkezés", "submit": "<PERSON><PERSON><PERSON><PERSON>", "alternate_provider_separator": "vagy"}, "orders": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order_number": "Megrendelés", "order_number_link": "<PERSON><PERSON><PERSON> s<PERSON>: {{ number }}", "date": "<PERSON><PERSON><PERSON>", "payment_status": "<PERSON><PERSON><PERSON><PERSON>", "fulfillment_status": "Teljesí<PERSON><PERSON>", "total": "Végösszeg", "none": "Még nem rendeltél semmit."}, "recover_password": {"title": "Új jelszó létrehozás<PERSON>", "subtext": "<PERSON><PERSON><PERSON><PERSON><PERSON> egy e-mailt, am<PERSON>el új j<PERSON>ót készíthetsz magadnak.", "success": "E-mailben elküldtük a jelszó módosításához szükséges hivatkozást."}, "register": {"title": "Fiók létrehozása", "first_name": "U<PERSON><PERSON><PERSON>v", "last_name": "Vezetéknév", "email": "E-mail-cím", "password": "Je<PERSON><PERSON><PERSON>", "submit": "Létrehozás"}, "reset_password": {"title": "Új fiókjelszó létrehozása", "subtext": "<PERSON><PERSON> be az ú<PERSON>", "password": "Je<PERSON><PERSON><PERSON>", "password_confirm": "<PERSON><PERSON><PERSON>ó megerősítése", "submit": "<PERSON><PERSON> j<PERSON>"}, "order": {"title": "Megrendelés: {{ name }}", "date_html": "Megrendelés dá<PERSON>a: {{ date }}", "cancelled_html": "Megrendelés lemondva: {{ date }}", "cancelled_reason": "Ok: {{ reason }}", "billing_address": "Számlázási cím", "payment_status": "<PERSON><PERSON><PERSON><PERSON>", "shipping_address": "Szállítási cím", "fulfillment_status": "Teljesí<PERSON><PERSON>", "discount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shipping": "Szállítás", "tax": "<PERSON><PERSON>", "product": "Termék", "sku": "Termékváltozat", "price": "<PERSON><PERSON>", "quantity": "Mennyiség", "total": "Végösszeg", "fulfilled_at_html": "Teljes<PERSON><PERSON><PERSON>: {{ date }}", "track_shipment": "Csomagkövetés", "tracking_url": "Hivatkozás a csomagkövetéshez", "tracking_company": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tracking_number": "Fu<PERSON>levélszá<PERSON>", "subtotal": "Részösszeg", "total_duties": "Vámok", "total_refunded": "Visszatérítve"}}, "gift_cards": {"issued": {"title": "Íme a(z) {{ shop }} üzletben levásárolható, {{ value }} értékű ajándékkártyád!", "subtext": "<PERSON>j<PERSON><PERSON>ékk<PERSON><PERSON><PERSON>", "gift_card_code": "Ajándékkártya kódja", "shop_link": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add_to_apple_wallet": "Hozzáadás az Apple Wallethoz", "qr_image_alt": "Ezt a QR-kódot beszkennelve beválthatod az ajándékkártyát.", "copy_code": "Ajándékkártya kódjának másolása", "expired": "<PERSON><PERSON><PERSON><PERSON>", "copy_code_success": "Sikeres volt a kód másolása", "how_to_use_gift_card": "Az ajándékkártya kódja online, a QR-kód pedig az üzletben használható fel", "expiration_date": "<PERSON><PERSON><PERSON><PERSON>: {{ expires_on }}"}}, "recipient": {"form": {"checkbox": "Ajándékba szeretném küldeni", "email_label": "Címzett e-mail-címe", "email": "E-mail-cím", "name_label": "Címzett neve (nem kötelező)", "name": "Név", "message_label": "Üzenet (nem kötelező)", "message": "Üzenet", "max_characters": "Maximum {{ max_chars }} karakter", "email_label_optional_for_no_js_behavior": "Címzett e-mail-címe (nem kötelező)", "send_on": "ÉÉÉÉ-HH-NN", "send_on_label": "<PERSON><PERSON><PERSON><PERSON> (nem kötelező)", "expanded": "Ajándékkártya címzettjének űrlapja kibontva", "collapsed": "Ajándékkártya címzettjének űrlapja összecsukva"}}}