.countdown-timer {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  text-align: center;
  color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.countdown-timer__content {
  max-width: 600px;
  margin: 0 auto;
}

.countdown-timer__title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.countdown-timer__display {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.countdown-timer__unit {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  min-width: 80px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.countdown-timer__number {
  display: block;
  font-size: 2.5rem;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 0.5rem;
  font-family: 'Courier New', monospace;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.countdown-timer__label {
  display: block;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.9;
}

.countdown-timer__message {
  font-size: 1rem;
  opacity: 0.9;
  line-height: 1.5;
}

.countdown-timer--loading {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.countdown-timer--error {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.countdown-timer__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.countdown-timer .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media screen and (max-width: 768px) {
  .countdown-timer {
    padding: 1.5rem;
    margin: 1rem 0;
  }
  
  .countdown-timer__title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .countdown-timer__display {
    gap: 1rem;
  }
  
  .countdown-timer__unit {
    padding: 0.75rem;
    min-width: 70px;
  }
  
  .countdown-timer__number {
    font-size: 2rem;
  }
  
  .countdown-timer__label {
    font-size: 0.75rem;
  }
}

@media screen and (max-width: 480px) {
  .countdown-timer__display {
    gap: 0.5rem;
  }
  
  .countdown-timer__unit {
    padding: 0.5rem;
    min-width: 60px;
  }
  
  .countdown-timer__number {
    font-size: 1.5rem;
  }
}

/* Coming Soon Message Fallback */
.coming-soon-message {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  text-align: center;
  color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.coming-soon-message h3 {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.coming-soon-message p {
  font-size: 1rem;
  opacity: 0.9;
  line-height: 1.5;
  margin: 0;
}

@media screen and (max-width: 768px) {
  .coming-soon-message {
    padding: 1.5rem;
    margin: 1rem 0;
  }

  .coming-soon-message h3 {
    font-size: 1.5rem;
  }
}
