.countdown-timer {
  background: rgb(var(--color-button));
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  text-align: center;
  color: rgb(var(--color-button-text));
  box-shadow: 0 10px 30px rgba(var(--color-shadow), 0.2);
  border: 1px solid rgba(var(--color-foreground), 0.1);
}

.countdown-timer__content {
  max-width: 600px;
  margin: 0 auto;
}

.countdown-timer__title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  color: rgb(var(--color-button-text));
}

.countdown-timer__display {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.countdown-timer__unit {
  background: rgba(var(--color-background), 0.1);
  border-radius: 8px;
  padding: 1rem;
  min-width: 80px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(var(--color-button-text), 0.2);
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.countdown-timer__unit:hover {
  transform: translateY(-2px);
  background: rgba(var(--color-background), 0.15);
}

.countdown-timer__number {
  display: block;
  font-size: 2.5rem;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 0.5rem;
  font-family: 'Courier New', monospace;
  text-shadow: 0 2px 4px rgba(var(--color-shadow), 0.3);
}

.countdown-timer__label {
  display: block;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.9;
}

.countdown-timer__message {
  font-size: 1rem;
  opacity: 0.9;
  line-height: 1.5;
}

.countdown-timer--loading {
  background: rgb(var(--color-secondary-button));
  color: rgb(var(--color-secondary-button-text));
}

.countdown-timer--error {
  background: rgb(var(--color-button));
  color: rgb(var(--color-button-text));
  opacity: 0.8;
}

.countdown-timer__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.countdown-timer .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(var(--color-secondary-button-text), 0.3);
  border-top: 4px solid rgb(var(--color-secondary-button-text));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.countdown-timer__number {
  animation: pulse 2s ease-in-out infinite;
}

/* Responsive design */
@media screen and (max-width: 768px) {
  .countdown-timer {
    padding: 1.5rem;
    margin: 1rem 0;
  }
  
  .countdown-timer__title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .countdown-timer__display {
    gap: 1rem;
  }
  
  .countdown-timer__unit {
    padding: 0.75rem;
    min-width: 60px;
  }
  
  .countdown-timer__number {
    font-size: 2rem;
  }
  
  .countdown-timer__label {
    font-size: 0.75rem;
  }
}

@media screen and (max-width: 480px) {
  .countdown-timer__display {
    gap: 0.5rem;
  }
  
  .countdown-timer__unit {
    padding: 0.5rem;
    min-width: 60px;
  }
  
  .countdown-timer__number {
    font-size: 1.5rem;
  }
}

/* Coming Soon Message Fallback */
.coming-soon-message {
  background: rgb(var(--color-button));
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  text-align: center;
  color: rgb(var(--color-button-text));
  box-shadow: 0 10px 30px rgba(var(--color-shadow), 0.2);
  border: 1px solid rgba(var(--color-foreground), 0.1);
}

.coming-soon-message h3 {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(var(--color-shadow), 0.3);
}

.coming-soon-message p {
  font-size: 1rem;
  opacity: 0.9;
  line-height: 1.5;
  margin: 0;
}

@media screen and (max-width: 768px) {
  .coming-soon-message {
    padding: 1.5rem;
    margin: 1rem 0;
  }

  .coming-soon-message h3 {
    font-size: 1.5rem;
  }
}
