<div class="complementary-product-card ts-relative">
	<a href="{{ product.url }}" class="link ts-block ts-aspect-square ts-p-4" aria-label="{{ product.title | escape }}">
		<span class="visually-hidden">{{ product.title }}</span>
		<div class="media ts-w-full ts-h-full">
			<img
				srcset="
					{%- if product.featured_media.width >= 165 -%}{{ product.featured_media | image_url: width: 165 }} 165w,{%- endif -%}
					{%- if product.featured_media.width >= 360 -%}{{ product.featured_media | image_url: width: 360 }} 360w,{%- endif -%}
					{%- if product.featured_media.width >= 533 -%}{{ product.featured_media | image_url: width: 533 }} 533w,{%- endif -%}
					{%- if product.featured_media.width >= 720 -%}{{ product.featured_media | image_url: width: 720 }} 720w,{%- endif -%}
					{%- if product.featured_media.width >= 940 -%}{{ product.featured_media | image_url: width: 940 }} 940w,{%- endif -%}
					{%- if product.featured_media.width >= 1066 -%}{{ product.featured_media | image_url: width: 1066 }} 1066w,{%- endif -%}
					{{ product.featured_media | image_url }} {{ product.featured_media.width }}w
				"
				src="{{ product.featured_media | image_url: width: 533 }}"
				sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 130 | divided_by: 4 }}px, (min-width: 990px) calc((100vw - 130px) / 4), (min-width: 750px) calc((100vw - 120px) / 3), calc((100vw - 35px) / 2)"
				alt="{{ product.featured_media.alt | escape }}"
				class="motion-reduce ts-object-contain"
				{% unless lazy_load == false %}
					loading="lazy"
				{% endunless %}
				width="{{ product.featured_media.width }}"
				height="{{ product.featured_media.height }}"
			>
		</div>
		<div class="card__badge ts-absolute ts-top-0 ts-left-0">
			{%- if product.available == false -%}
				<span
					id="NoMediaStandardBadge-{{ section_id }}-{{ product.id }}"
					class="badge ts-border-0 ts-p-0"
				>
					{{- 'products.product.sold_out' | t -}}
				</span>
			{%- elsif product.compare_at_price > product.price and product.available -%}
				<span
					id="NoMediaStandardBadge-{{ section_id }}-{{ product.id }}"
					class="badge ts-border-0 ts-p-0"
				>
					{{- 'products.product.on_sale' | t -}}
				</span>
			{%- endif -%}
		</div>
	</a>
</div>