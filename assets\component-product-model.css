.product__xr-button {
  background: rgba(var(--color-foreground), 0.08);
  color: rgb(var(--color-foreground));
  margin: 1rem auto;
  box-shadow: none;
  display: flex;
}

.button.product__xr-button:hover {
  box-shadow: none;
}

.product__xr-button[data-shopify-xr-hidden] {
  visibility: hidden;
}

.shopify-design-mode .product__xr-button[data-shopify-xr-hidden] {
  display: none;
}

@media screen and (max-width: 749px) {
  slider-component .product__xr-button {
    display: none;
  }

  .active .product__xr-button:not([data-shopify-xr-hidden]) {
    display: block;
  }
}

@media screen and (min-width: 750px) {
  slider-component + .button.product__xr-button {
    display: none;
  }

  .product__xr-button[data-shopify-xr-hidden] {
    display: none;
  }
}

.product__xr-button .icon {
  width: 1.4rem;
  margin-right: 1rem;
}
