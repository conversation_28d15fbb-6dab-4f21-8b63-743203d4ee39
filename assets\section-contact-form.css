.contact img {
  max-width: 100%;
}
.contact .body-text p:not(:last-of-type) {
  margin-bottom: clamp(1.5rem, 2vw, 1.8rem);
}
.contact .form__message {
  align-items: flex-start;
}

.contact .icon-success {
  margin-top: 0.2rem;
}

.contact .field {
  margin-bottom: 1rem;
}
.contact .field::after {
  box-shadow: 0 0 0 1px rgba(var(--color-foreground), 0.1);
}
.contact .field .field__input {
  background-color: #f9f9f9;
}
.field__label {
  font-size: 1.5rem;
  opacity: 0.55;
}
.contact__button {
  margin-top: 1.25rem;
}

@media screen and (min-width: 750px) {
  .contact__button {
    margin-top: 2rem;
  }
}

@media screen and (min-width: 750px) {
  .contact__fields {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 2rem;
  }
}
