{"general": {"password_page": {"login_form_heading": "<PERSON><PERSON><PERSON> c<PERSON>a hàng bằng mật khẩu:", "login_password_button": "Vào bằng mật khẩu", "login_form_password_label": "<PERSON><PERSON><PERSON>", "login_form_password_placeholder": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>a bạn", "login_form_error": "Sai mật khẩu!", "login_form_submit": "Vào", "admin_link_html": "Bạn có phải chủ cửa hàng không? <a href=\"/admin\" class=\"link underlined-link\"><PERSON><PERSON><PERSON> nhập tại đây</a>", "powered_by_shopify_html": "<PERSON><PERSON><PERSON> hàng này sẽ do {{ shopify }} cung cấp"}, "social": {"alt_text": {"share_on_facebook": "Chia sẻ trên Facebook", "share_on_twitter": "Chia sẻ trên X", "share_on_pinterest": "<PERSON><PERSON> tr<PERSON><PERSON>"}, "links": {"twitter": "X (Twitter)", "facebook": "Facebook", "pinterest": "Pinterest", "instagram": "Instagram", "tumblr": "Tumblr", "snapchat": "Snapchat", "youtube": "YouTube", "vimeo": "Vimeo", "tiktok": "TikTok"}}, "continue_shopping": "<PERSON><PERSON><PERSON><PERSON> tục mua sắm", "pagination": {"label": "<PERSON><PERSON> trang", "page": "Trang {{ number }}", "next": "Trang sau", "previous": "<PERSON><PERSON> tr<PERSON>"}, "search": {"search": "<PERSON><PERSON><PERSON>", "reset": "<PERSON><PERSON><PERSON> cụm từ tìm kiếm"}, "cart": {"view": "Xem giỏ hàng ({{ count }})", "item_added": "Mặt hàng đã thêm vào giỏ hàng", "view_empty_cart": "<PERSON>em giỏ hàng"}, "share": {"copy_to_clipboard": "<PERSON><PERSON> ch<PERSON><PERSON> liên kết", "share_url": "<PERSON><PERSON><PERSON>", "success_message": "Đã sao chép liên kết vào bảng nhớ tạm", "close": "<PERSON><PERSON><PERSON> c<PERSON>a sổ chia sẻ"}, "slider": {"of": "trong số", "next_slide": "<PERSON><PERSON><PERSON><PERSON><PERSON> sang phải", "previous_slide": "<PERSON><PERSON><PERSON><PERSON><PERSON> sang trái", "name": "<PERSON><PERSON>"}}, "newsletter": {"label": "Email", "success": "<PERSON><PERSON><PERSON> ơn bạn đã đăng ký", "button_label": "<PERSON><PERSON><PERSON> ký"}, "accessibility": {"skip_to_text": "<PERSON><PERSON><PERSON><PERSON> đến nội dung", "close": "Đ<PERSON><PERSON>", "unit_price_separator": "trên", "vendor": "<PERSON><PERSON>à cung cấp:", "error": "Lỗi", "refresh_page": "<PERSON><PERSON> bạn chọn một mục, toàn bộ trang sẽ được làm mới.", "link_messages": {"new_window": "Mở trong cửa sổ mới.", "external": "Mở trang web bên ngo<PERSON>i."}, "loading": "<PERSON><PERSON> tả<PERSON>...", "skip_to_product_info": "<PERSON>y<PERSON>n đến thông tin sản phẩm", "total_reviews": "tổng số lư<PERSON><PERSON> đ<PERSON> giá", "star_reviews_info": "{{ rating_value }}/{{ rating_max }} sao", "collapsible_content_title": "<PERSON><PERSON><PERSON> dung có thể thu gọn", "complementary_products": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> b<PERSON> sung"}, "blogs": {"article": {"blog": "Blog", "read_more_title": "<PERSON><PERSON><PERSON> thêm: {{ title }}", "comments": {"one": "{{ count }} b<PERSON><PERSON> lu<PERSON>n", "other": "{{ count }} b<PERSON><PERSON> lu<PERSON>n"}, "moderated": "<PERSON>n lưu ý, bình luận cần đư<PERSON><PERSON> phê duyệt trước khi được đăng.", "comment_form_title": "<PERSON><PERSON> lại bình luận", "name": "<PERSON><PERSON><PERSON>", "email": "Email", "message": "<PERSON><PERSON><PERSON> lu<PERSON>", "post": "<PERSON><PERSON><PERSON> b<PERSON>nh lu<PERSON>n", "back_to_blog": "Quay lại blog", "share": "<PERSON>a sẻ bài viết này", "success": "Bạn đã gửi bình luận thành công! Xin cảm ơn!", "success_moderated": "Bạn đã gửi bình luận thành công. Chúng tôi sẽ đăng bình luận sau chốc lát, khi blog của chúng tôi được kiểm duyệt."}}, "onboarding": {"product_title": "<PERSON><PERSON><PERSON><PERSON> đề sản phẩm mẫu", "collection_title": "<PERSON><PERSON><PERSON> bộ sưu tập của bạn"}, "products": {"product": {"add_to_cart": "Thêm vào giỏ hàng", "description": "<PERSON><PERSON>", "on_sale": "G<PERSON>ảm giá", "product_variants": "Mẫu mã sản phẩm", "quantity": {"label": "Số lượng", "input_label": "S<PERSON> l<PERSON> củ<PERSON> {{ product }}", "increase": "<PERSON><PERSON>ng số lượng của {{ product }}", "decrease": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> l<PERSON> củ<PERSON> {{ product }}", "minimum_of": "<PERSON><PERSON><PERSON> thiểu {{ quantity }}", "maximum_of": "T<PERSON>i đa {{ quantity }}", "multiples_of": "S<PERSON> lượng gia tăng {{ quantity }}", "in_cart_html": "<span class=\"quantity-cart\">{{ quantity }}</span> trong giỏ hàng", "note": "<PERSON><PERSON> quy tắc số lượng", "min_of": "<PERSON><PERSON><PERSON> thiểu {{ quantity }}", "max_of": "T<PERSON>i đa {{ quantity }}"}, "price": {"from_price_html": "Từ {{ price }}", "regular_price": "<PERSON><PERSON><PERSON> thông thường", "sale_price": "<PERSON><PERSON><PERSON> <PERSON>u đãi", "unit_price": "Đơn giá"}, "share": "<PERSON><PERSON> sẻ sản phẩm này", "sold_out": "<PERSON><PERSON> b<PERSON> h<PERSON>t", "unavailable": "<PERSON><PERSON><PERSON>ng có sẵn", "vendor": "<PERSON><PERSON><PERSON> cung cấp", "video_exit_message": "{{ title }} mở video toàn màn hình ở cùng một cửa sổ.", "xr_button": "<PERSON><PERSON> tại không gian của bạn", "xr_button_label": "<PERSON><PERSON> tại không gian củ<PERSON> b<PERSON>, tả<PERSON> mặt hàng trong cửa sổ thực tế ảo tăng cường", "pickup_availability": {"view_store_info": "<PERSON><PERSON> thông tin cửa hàng", "check_other_stores": "<PERSON><PERSON><PERSON> tra tình trạng còn hàng tại các cửa hàng khác", "pick_up_available": "<PERSON><PERSON> thể nhận hàng tại cửa hàng", "pick_up_available_at_html": "<PERSON><PERSON> thể nhận hàng tại <span class=\"color-foreground\">{{ location_name }}</span>", "pick_up_unavailable_at_html": "<PERSON><PERSON><PERSON> chưa thể nhận hàng tại <span class=\"color-foreground\">{{ location_name }}</span>", "unavailable": "<PERSON><PERSON><PERSON><PERSON> thể tải khả năng nhận hàng tại cửa hàng", "refresh": "<PERSON><PERSON><PERSON>"}, "media": {"open_media": "Mở phương tiện {{ index }} trong hộp tương tác", "play_model": "Mở Trình xem 3D", "play_video": "<PERSON><PERSON><PERSON> video", "gallery_viewer": "<PERSON><PERSON><PERSON><PERSON> xem thư viện", "load_image": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON> {{ index }} trong chế độ xem thư viện", "load_model": "T<PERSON><PERSON> mô hình 3D {{ index }} trong chế độ xem thư viện", "load_video": "<PERSON><PERSON><PERSON> video {{ index }} trong chế độ xem thư viện", "image_available": "<PERSON><PERSON><PERSON> {{ index }} hiện đã có trong chế độ xem thư viện"}, "view_full_details": "<PERSON><PERSON>n bộ chi tiết", "shipping_policy_html": "<a href=\"{{ link }}\"><PERSON><PERSON> vận chuyển</a> <PERSON><PERSON><PERSON><PERSON> t<PERSON>h khi thanh to<PERSON>.", "choose_options": "<PERSON><PERSON><PERSON> c<PERSON>c tùy chọn", "choose_product_options": "<PERSON><PERSON><PERSON> tùy chọn cho {{ product_name }}", "value_unavailable": "{{ option_value }} - <PERSON><PERSON><PERSON>ng khả dụng", "variant_sold_out_or_unavailable": "Mẫu mã đã bán hết hoặc không còn hàng", "inventory_in_stock": "<PERSON><PERSON><PERSON> hàng", "inventory_in_stock_show_count": "Còn {{ quantity }} hàng lưu kho", "inventory_low_stock": "<PERSON><PERSON><PERSON> hế<PERSON> hàng", "inventory_low_stock_show_count": "<PERSON><PERSON><PERSON> hết hàng: Còn {{ quantity }}", "inventory_out_of_stock": "<PERSON><PERSON><PERSON>", "inventory_out_of_stock_continue_selling": "<PERSON><PERSON><PERSON> hàng", "sku": "SKU", "volume_pricing": {"title": "<PERSON><PERSON><PERSON> giá theo s<PERSON> l<PERSON>", "note": "<PERSON><PERSON> sẵn định giá theo số lượng", "minimum": "Hơn {{ quantity }}", "price_range": "{{ minimum }} - {{ maximum }}", "price_at_each_html": "với giá {{ price }}/sản phẩm"}, "taxes_included": "<PERSON><PERSON> bao gồm thu<PERSON>.", "duties_included": "<PERSON><PERSON> bao gồm thuế nhập kh<PERSON>u.", "duties_and_taxes_included": "<PERSON><PERSON> bao gồm thuế và thuế nhập khẩu."}, "modal": {"label": "<PERSON><PERSON><PERSON> viện ph<PERSON><PERSON>ng tiện"}, "facets": {"apply": "<PERSON><PERSON>", "clear": "Xóa", "clear_all": "<PERSON><PERSON><PERSON> tất cả", "from": "Từ", "filter_and_sort": "Lọc và sắp xếp", "filter_by_label": "Bộ lọc:", "filter_button": "<PERSON><PERSON> lọc", "filters_selected": {"one": "<PERSON><PERSON> chọn {{ count }}", "other": "<PERSON><PERSON> chọn {{ count }}"}, "max_price": "G<PERSON><PERSON> cao nhất là {{ price }}", "product_count": {"one": "{{ product_count }}/{{ count }} sản phẩm", "other": "{{ product_count }}/{{ count }} sản phẩm"}, "product_count_simple": {"one": "{{ count }} s<PERSON>n phẩm", "other": "{{ count }} s<PERSON>n phẩm"}, "reset": "Đặt lại", "sort_button": "<PERSON><PERSON><PERSON>p", "sort_by_label": "<PERSON><PERSON><PERSON> xếp theo:", "to": "<PERSON><PERSON><PERSON>", "clear_filter": "Xóa bộ lọc", "filter_selected_accessibility": "{{ type }} (<PERSON><PERSON> chọn {{ count }} bộ lọc)", "show_more": "<PERSON><PERSON><PERSON> thị thêm", "show_less": "<PERSON><PERSON><PERSON>", "filter_and_operator_subtitle": "<PERSON><PERSON> hợp với tất cả"}}, "templates": {"search": {"no_results": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả cho \"{{ terms }}\". <PERSON><PERSON><PERSON> tra chính tả hoặc sử dụng một từ hoặc cụm từ khác.", "results_with_count": {"one": "{{ count }} kết quả", "other": "{{ count }} kết quả"}, "title": "<PERSON><PERSON><PERSON> qu<PERSON> tìm kiếm", "page": "<PERSON><PERSON>", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "search_for": "<PERSON><PERSON><PERSON> “{{ terms }}”", "results_with_count_and_term": {"one": "<PERSON><PERSON><PERSON> thấy {{ count }} kế<PERSON> qu<PERSON> cho “{{ terms }}”", "other": "<PERSON><PERSON><PERSON> thấy {{ count }} kế<PERSON> qu<PERSON> cho “{{ terms }}”"}, "results_pages_with_count": {"one": "Trang {{ count }}", "other": "{{ count }} trang"}, "results_suggestions_with_count": {"one": "{{ count }} gợi ý", "other": "{{ count }} gợi ý"}, "results_products_with_count": {"one": "{{ count }} s<PERSON>n phẩm", "other": "{{ count }} s<PERSON>n phẩm"}, "suggestions": "G<PERSON><PERSON> ý", "pages": "<PERSON><PERSON>"}, "cart": {"cart": "Giỏ hàng"}, "contact": {"form": {"name": "<PERSON><PERSON><PERSON>", "email": "Email", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "comment": "<PERSON><PERSON><PERSON> lu<PERSON>", "send": "<PERSON><PERSON><PERSON>", "post_success": "Cảm ơn đã liên hệ với chúng tôi. Chúng tôi sẽ liên hệ lại với bạn trong thời gian sớm nhất.", "error_heading": "<PERSON><PERSON> lòng điều chỉnh các mục sau:", "title": "<PERSON>i<PERSON>u mẫu liên hệ"}}, "404": {"title": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trang", "subtext": "404"}}, "sections": {"header": {"announcement": "<PERSON><PERSON><PERSON><PERSON> báo", "menu": "<PERSON><PERSON>", "cart_count": {"one": "{{ count }} mặt hàng", "other": "{{ count }} mặt hàng"}}, "cart": {"title": "Giỏ hàng của bạn", "caption": "Các mặt hàng trong giỏ hàng", "remove_title": "<PERSON>ó<PERSON> {{ title }}", "note": "Hướng dẫn đặc biệt của đơn hàng", "checkout": "<PERSON><PERSON> toán", "empty": "Giỏ hàng của bạn đang trống", "cart_error": "<PERSON><PERSON> xảy ra lỗi khi cập nhật giỏ hàng. <PERSON><PERSON> lòng thử lại.", "cart_quantity_error_html": "Bạn chỉ có thể thêm {{ quantity }} mặt hàng này vào giỏ hàng.", "headings": {"product": "<PERSON><PERSON><PERSON> p<PERSON>m", "price": "Giá", "total": "Tổng", "quantity": "Số lượng", "image": "<PERSON><PERSON><PERSON> sản phẩm"}, "update": "<PERSON><PERSON><PERSON>", "login": {"title": "Bạn đã có tài k<PERSON>n?", "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\"><PERSON><PERSON><PERSON> nh<PERSON>p</a> để thanh to<PERSON> n<PERSON>h hơn."}, "estimated_total": "Tổng số tiền <PERSON><PERSON> t<PERSON>h", "new_estimated_total": "Tổng số tiền ước t<PERSON>h mới", "duties_and_taxes_included_shipping_at_checkout_with_policy_html": "<PERSON><PERSON> bao gồm thuế và thuế nhập khẩu. Ưu đãi giảm giá và <a href=\"{{ link }}\">phí vận chuyển</a> đ<PERSON><PERSON><PERSON> t<PERSON>h khi <PERSON>h toán.", "duties_and_taxes_included_shipping_at_checkout_without_policy": "<PERSON><PERSON> bao gồm thuế và thuế nhập khẩu. Ưu đãi giảm giá và phí vận chuyển đư<PERSON><PERSON> t<PERSON>h khi thanh toán.", "taxes_included_shipping_at_checkout_with_policy_html": "<PERSON><PERSON> bao gồm thuế. Ưu đãi giảm giá và <a href=\"{{ link }}\">phí vận chuyển</a> đ<PERSON><PERSON><PERSON> t<PERSON>h khi <PERSON>h toán.", "taxes_included_shipping_at_checkout_without_policy": "<PERSON><PERSON> bao gồm thuế. Ưu đãi giảm giá và phí vận chuyển đượ<PERSON> t<PERSON>h khi thanh toán.", "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_html": "<PERSON><PERSON> bao gồm thuế nhập khẩu. <PERSON><PERSON><PERSON>, <PERSON>u đãi giảm giá và <a href=\"{{ link }}\">ph<PERSON> vận chuyển</a> đ<PERSON><PERSON><PERSON> t<PERSON>h khi <PERSON>h to<PERSON>.", "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy": "<PERSON><PERSON> bao gồm thuế nhập khẩu. <PERSON><PERSON><PERSON>, ưu đãi giảm giá và phí vận chuyển đư<PERSON><PERSON> t<PERSON>h khi thanh toán.", "taxes_at_checkout_shipping_at_checkout_with_policy_html": "<PERSON><PERSON><PERSON>, <PERSON>u đãi giảm giá và <a href=\"{{ link }}\">phí vận chuyển</a> đ<PERSON><PERSON><PERSON> t<PERSON>h khi thanh toán.", "taxes_at_checkout_shipping_at_checkout_without_policy": "<PERSON><PERSON><PERSON>, ưu đãi giảm giá và phí vận chuyển đư<PERSON><PERSON> t<PERSON>h khi thanh toán."}, "footer": {"payment": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán"}, "featured_blog": {"view_all": "<PERSON><PERSON> t<PERSON>t cả", "onboarding_title": "<PERSON><PERSON><PERSON> v<PERSON> blog", "onboarding_content": "<PERSON> khách hàng xem tóm tắt bài viết blog"}, "featured_collection": {"view_all": "<PERSON><PERSON> t<PERSON>t cả", "view_all_label": "<PERSON>em toàn bộ sản phẩm trong bộ sưu tập {{ collection_name }}"}, "collection_list": {"view_all": "<PERSON><PERSON> t<PERSON>t cả"}, "collection_template": {"title": "<PERSON><PERSON> s<PERSON>u tập", "empty": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm", "use_fewer_filters_html": "Sử dụng ít bộ lọc hơn hoặc <a class=\"{{ class }}\" href=\"{{ link }}\">x<PERSON><PERSON> tất cả</a>"}, "video": {"load_video": "T<PERSON>i video: {{ description }}"}, "slideshow": {"load_slide": "<PERSON><PERSON><PERSON> trang chi<PERSON>u", "previous_slideshow": "<PERSON><PERSON> chi<PERSON> tr<PERSON>", "next_slideshow": "<PERSON><PERSON> chi<PERSON>u sau", "pause_slideshow": "<PERSON><PERSON><PERSON> dừng bản trình chi<PERSON>u", "play_slideshow": "<PERSON><PERSON><PERSON> bản trình ch<PERSON>", "carousel": "Quay vòng", "slide": "<PERSON><PERSON>"}, "page": {"title": "Ti<PERSON><PERSON> đề trang"}, "announcements": {"previous_announcement": "<PERSON><PERSON><PERSON><PERSON> báo trước", "next_announcement": "<PERSON><PERSON><PERSON><PERSON> báo sau", "carousel": "Quay vòng", "announcement": "<PERSON><PERSON><PERSON><PERSON> báo", "announcement_bar": "<PERSON><PERSON> thông báo"}, "quick_order_list": {"product_total": "<PERSON>ổng ph<PERSON> sản phẩm", "view_cart": "<PERSON>em giỏ hàng", "each": "{{ money }}/chiếc", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "variant": "Mẫu mã", "variant_total": "Tổng số mẫu mã", "items_added": {"one": "<PERSON>ã thêm {{ quantity }} mặt hàng", "other": "<PERSON>ã thêm {{ quantity }} mặt hàng"}, "items_removed": {"one": "Đã xóa {{ quantity }} mặt hàng", "other": "Đã xóa {{ quantity }} mặt hàng"}, "product_variants": "Mẫu mã sản phẩm", "total_items": "Tổng số mặt hàng", "remove_all_items_confirmation": "<PERSON><PERSON><PERSON> tất cả {{ quantity }} mặt hàng khỏi giỏ hàng của bạn?", "remove_all": "<PERSON><PERSON><PERSON> tất cả", "cancel": "<PERSON><PERSON><PERSON>", "remove_all_single_item_confirmation": "Xóa 1 mặt hàng khỏi giỏ hàng?", "min_error": "Mặt hàng này có tối thiểu {{ min }}", "max_error": "Mặt hàng này có tối đa {{ max }}", "step_error": "Bạn chỉ có thể thêm mặt hàng này theo đơn vị giao dịch {{ step }}"}}, "localization": {"country_label": "Quốc gia/khu vực", "language_label": "<PERSON><PERSON><PERSON>", "update_language": "<PERSON><PERSON><PERSON> nh<PERSON>t ngôn ngữ", "update_country": "<PERSON><PERSON><PERSON> nhật quốc gia/khu vực", "search": "<PERSON><PERSON><PERSON>", "popular_countries_regions": "Quốc gia/khu vực phổ biến", "country_results_count": "<PERSON><PERSON> tìm thấy {{ count }} quốc gia/khu vực"}, "customer": {"account": {"title": "<PERSON><PERSON><PERSON>", "details": "<PERSON> tiết tài k<PERSON>n", "view_addresses": "<PERSON><PERSON> chỉ", "return": "Quay lại Chi tiết tài k<PERSON>n"}, "account_fallback": "<PERSON><PERSON><PERSON>", "activate_account": {"title": "<PERSON><PERSON><PERSON> ho<PERSON> tà<PERSON>", "subtext": "<PERSON><PERSON><PERSON> mật khẩu để kích hoạt tài k<PERSON>n.", "password": "<PERSON><PERSON><PERSON>", "password_confirm": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "submit": "<PERSON><PERSON><PERSON> ho<PERSON> tà<PERSON>", "cancel": "Từ chối lời mời"}, "addresses": {"title": "Địa chỉ", "default": "Mặc định", "add_new": "<PERSON><PERSON><PERSON><PERSON> địa chỉ mới", "edit_address": "<PERSON><PERSON><PERSON> địa chỉ", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Họ", "company": "<PERSON><PERSON>ng ty", "address1": "Địa chỉ 1", "address2": "Địa chỉ 2", "city": "<PERSON><PERSON><PERSON><PERSON> phố", "country": "Quốc gia/khu vực", "province": "Tỉnh", "zip": "<PERSON>ã bưu ch<PERSON>/mã ZIP", "phone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "set_default": "Đặt làm địa chỉ mặc định", "add": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "update": "<PERSON><PERSON><PERSON> nh<PERSON>t địa chỉ", "cancel": "<PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "delete": "Xóa", "delete_confirm": "Bạn có chắc chắn muốn xóa địa chỉ này không?"}, "log_in": "<PERSON><PERSON><PERSON>", "log_out": "<PERSON><PERSON><PERSON> xu<PERSON>", "login_page": {"cancel": "<PERSON><PERSON><PERSON>", "create_account": "<PERSON><PERSON><PERSON> t<PERSON>", "email": "Email", "forgot_password": "<PERSON>uên mật khẩu?", "guest_continue": "<PERSON><PERSON><PERSON><PERSON>", "guest_title": "<PERSON><PERSON><PERSON><PERSON> tục với tư cách kh<PERSON>ch", "password": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "sign_in": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "alternate_provider_separator": "hoặc"}, "orders": {"title": "<PERSON><PERSON><PERSON> sử đặt hàng", "order_number": "<PERSON><PERSON><PERSON> hàng", "order_number_link": "<PERSON><PERSON> đơn hàng {{ number }}", "date": "<PERSON><PERSON><PERSON>", "payment_status": "<PERSON>r<PERSON><PERSON> thái thanh toán", "fulfillment_status": "<PERSON><PERSON><PERSON><PERSON> thái thực hi<PERSON>n", "total": "Tổng", "none": "Bạn chưa đặt đơn hàng nào."}, "recover_password": {"title": "Đặt lại mật khẩu", "subtext": "<PERSON><PERSON><PERSON> tôi sẽ gửi email cho bạn để đặt lại mật khẩu", "success": "<PERSON><PERSON>g tôi đã gửi cho bạn email chứa liên kết cập nhật mật khẩu."}, "register": {"title": "<PERSON><PERSON><PERSON> t<PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Họ", "email": "Email", "password": "<PERSON><PERSON><PERSON>", "submit": "Tạo"}, "reset_password": {"title": "Đặt lại mật khẩu tài k<PERSON>n", "subtext": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u mới", "password": "<PERSON><PERSON><PERSON>", "password_confirm": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "submit": "Đặt lại mật khẩu"}, "order": {"title": "<PERSON><PERSON><PERSON> hàng {{ name }}", "date_html": "Đặt vào {{ date }}", "cancelled_html": "Đ<PERSON><PERSON> hàng đã bị hủy vào {{ date }}", "cancelled_reason": "Lý do: {{ reason }}", "billing_address": "<PERSON><PERSON><PERSON> chỉ thanh toán", "payment_status": "<PERSON>r<PERSON><PERSON> thái thanh toán", "shipping_address": "Đ<PERSON>a chỉ giao hàng", "fulfillment_status": "<PERSON><PERSON><PERSON><PERSON> thái thực hi<PERSON>n", "discount": "G<PERSON>ảm giá", "shipping": "<PERSON><PERSON><PERSON> ch<PERSON>", "tax": "<PERSON><PERSON><PERSON>", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "sku": "SKU", "price": "Giá", "quantity": "Số lượng", "total": "Tổng", "fulfilled_at_html": "<PERSON><PERSON> thực hi<PERSON>n {{ date }}", "track_shipment": "<PERSON> l<PERSON> h<PERSON>ng", "tracking_url": "<PERSON><PERSON><PERSON> kết theo dõi", "tracking_company": "<PERSON><PERSON><PERSON> vận chuy<PERSON>n", "tracking_number": "<PERSON><PERSON> theo dõi", "subtotal": "Tổng phụ", "total_duties": "<PERSON><PERSON><PERSON> hải quan", "total_refunded": "<PERSON><PERSON> hoàn tiền"}}, "gift_cards": {"issued": {"title": "<PERSON><PERSON><PERSON> là số dư thẻ quà tặng trị giá {{ value }} của bạn cho {{ shop }}!", "subtext": "Thẻ quà tặng của bạn", "gift_card_code": "Mã thẻ quà tặng", "shop_link": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> c<PERSON><PERSON> hàng tr<PERSON><PERSON> tuyến", "add_to_apple_wallet": "Thêm vào Apple Wallet", "qr_image_alt": "Mã QR — quét để đổi thẻ quà tặng", "copy_code": "Sao chép mã thẻ quà tặng", "expired": "<PERSON><PERSON> hết hạn", "copy_code_success": "Đã sao chép mã thành công", "how_to_use_gift_card": "Sử dụng mã thẻ quà tặng trực tuyến hoặc mã QR tại cửa hàng", "expiration_date": "<PERSON><PERSON><PERSON> hạn vào {{ expires_on }}"}}, "recipient": {"form": {"checkbox": "<PERSON><PERSON>i muốn gửi làm quà", "email_label": "<PERSON><PERSON>", "email": "Email", "name_label": "<PERSON><PERSON><PERSON><PERSON> (kh<PERSON><PERSON> bắ<PERSON> buộc)", "name": "<PERSON><PERSON><PERSON>", "message_label": "<PERSON> (không bắt bu<PERSON>c)", "message": "<PERSON>", "max_characters": "<PERSON><PERSON>i đa {{ max_chars }} ký tự", "email_label_optional_for_no_js_behavior": "<PERSON><PERSON> (k<PERSON><PERSON><PERSON> bắt bu<PERSON>)", "send_on": "DD-MM-YYYY", "send_on_label": "<PERSON><PERSON><PERSON> (t<PERSON><PERSON>)", "expanded": "Đã mở rộng biểu mẫu người nhận thẻ quà tặng", "collapsed": "<PERSON>ã thu nhỏ biểu mẫu người nhận thẻ quà tặng"}}}